package com.clickzetta.rm.scheduler;

import com.clickzetta.rm.common.exception.OperationCancelledException;
import com.clickzetta.rm.common.exception.ServerBusyException;
import com.clickzetta.rm.scheduler.meta.InMemorySchedulerActionMetaClient;
import com.clickzetta.rm.scheduler.operator.HelloOperator;
import com.clickzetta.rm.scheduler.operator.RetryOperator;
import com.clickzetta.rm.scheduler.operator.SleepOperator;
import org.apache.hadoop.thirdparty.com.google.common.collect.ImmutableMap;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.stubbing.Answer;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class ActionTest {
  private ActionScheduler scheduler;
  InMemorySchedulerActionMetaClient metaClient = new InMemorySchedulerActionMetaClient();

  @Before
  public void setup() {
    ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(4);
    scheduler = new ActionScheduler(scheduledExecutorService, metaClient);
  }

  @Test(timeout = 10000)
  public void testNormal() throws Exception {
    ActionDriver driver = scheduler.getOrCreateActionDriver("test-0");
    Map<String, String> settings = new HashMap<>();
    settings.put("key", "blabla");
    HelloOperator first = new HelloOperator();
    HelloOperator second = new HelloOperator();
    HelloOperator third = new HelloOperator();
    RetryOperator retry = new RetryOperator();
    first.onSuccess(retry)
        .onFailure(third)
        .setProperties(ImmutableMap.of(HelloOperator.MSG, "first"))
        .setRetryPolicy(new RetryPolicy());
    retry.onSuccess(second)
        .onFailure(third)
        .setProperties(ImmutableMap.of(RetryOperator.MAX_RETRY_COUNT, "3"))
        .setRetryPolicy(new RetryPolicy().setMaxRetryCount(4));
    second.setRetryPolicy(new RetryPolicy()).setProperties(ImmutableMap.of(HelloOperator.MSG, "second"));
    third.setRetryPolicy(new RetryPolicy()).setProperties(ImmutableMap.of(HelloOperator.MSG, "third"));
    Action action = Action.create("test-0", first, settings);
    driver.submit(action);
    driver.waitForFinish(1000000);
    Assert.assertEquals(action.getState(), ActionState.COMPLETED);
    Assert.assertEquals(first.getRetryPolicy().getAttemptCount(), 1);
    Assert.assertEquals(second.getRetryPolicy().getAttemptCount(), 1);
    Assert.assertEquals(retry.getRetryPolicy().getAttemptCount(), 4);
    Assert.assertEquals(first.getRetryPolicy().getLastFailure(), null);
    Assert.assertEquals(second.getRetryPolicy().getLastFailure(), null);
    Assert.assertEquals(retry.getRetryPolicy().getLastFailure(), null);
    Assert.assertEquals(third.getRetryPolicy().getAttemptCount(), 0);
    scheduler.removeActionDriver("test-0");
  }

  @Test(timeout = 10000)
  public void testException() throws Exception {
    ActionDriver driver = scheduler.getOrCreateActionDriver("test-1");
    Map<String, String> settings = new HashMap<>();
    settings.put("key", "blabla");
    HelloOperator first = new HelloOperator();
    HelloOperator second = new HelloOperator();
    HelloOperator third = new HelloOperator();
    RetryOperator retry = new RetryOperator();
    first.onSuccess(retry)
        .onFailure(third)
        .setProperties(ImmutableMap.of(HelloOperator.MSG, "first"))
        .setRetryPolicy(new RetryPolicy());
    retry.onSuccess(second)
        .onFailure(third)
        .setProperties(ImmutableMap.of(RetryOperator.MAX_RETRY_COUNT, "3"))
        .setRetryPolicy(new RetryPolicy().setMaxRetryCount(3));
    second.setRetryPolicy(new RetryPolicy()).setProperties(ImmutableMap.of(HelloOperator.MSG, "second"));
    third.setRetryPolicy(new RetryPolicy()).setProperties(ImmutableMap.of(HelloOperator.MSG, "third"));
    Action action = Action.create("test-1", first, settings);
    driver.submit(action);
    driver.waitForFinish(1000000);
    Assert.assertEquals(action.getState(), ActionState.COMPLETED);
    Assert.assertEquals(first.getRetryPolicy().getAttemptCount(), 1);
    Assert.assertEquals(second.getRetryPolicy().getAttemptCount(), 0);
    Assert.assertEquals(third.getRetryPolicy().getAttemptCount(), 1);
    Assert.assertEquals(retry.getRetryPolicy().getAttemptCount(), 3);

    Assert.assertEquals(first.getRetryPolicy().getLastFailure(), null);
    Assert.assertEquals(second.getRetryPolicy().getLastFailure(), null);
    Assert.assertEquals(third.getRetryPolicy().getLastFailure(), null);
    Assert.assertEquals(retry.getRetryPolicy().getLastFailure(), retry.getException());
    scheduler.removeActionDriver("test-1");
  }

  @Test(timeout = 10000)
  public void testCancellableOperator() throws Exception {
    ActionDriver driver = scheduler.getOrCreateActionDriver("test-2");
    Map<String, String> settings = new HashMap<>();
    settings.put("key", "blabla");
    settings.put(SleepOperator.SLEEP_TIME_MS, "10000");
    settings.put(SleepOperator.CHECK_CANCELLED, "true");
    SleepOperator sleepOperator = new SleepOperator();
    HelloOperator first = new HelloOperator();
    HelloOperator second = new HelloOperator();
    first.setRetryPolicy(new RetryPolicy()).setProperties(ImmutableMap.of(HelloOperator.MSG, "first"));
    second.setRetryPolicy(new RetryPolicy()).setProperties(ImmutableMap.of(HelloOperator.MSG, "second"));
    sleepOperator.onSuccess(first)
        .onFailure(second)
        .setProperties(ImmutableMap.of(SleepOperator.SLEEP_TIME_MS, "10000",
            SleepOperator.CHECK_CANCELLED, "true"))
        .setRetryPolicy(new RetryPolicy());
    Action action = Action.create("test-2", sleepOperator, settings);
    driver.submit(action);
    Thread.sleep(200);
    driver.cancel();
    driver.waitForFinish(1000000);
    Assert.assertEquals(action.getState(), ActionState.COMPLETED);
    Assert.assertEquals(first.getRetryPolicy().getAttemptCount(), 0);
    Assert.assertEquals(second.getRetryPolicy().getAttemptCount(), 1);
    Assert.assertEquals(sleepOperator.getRetryPolicy().getAttemptCount(), 1);

    Assert.assertEquals(first.getRetryPolicy().getLastFailure(), null);
    Assert.assertEquals(second.getRetryPolicy().getLastFailure(), null);
    Assert.assertTrue(sleepOperator.getRetryPolicy().getLastFailure() instanceof OperationCancelledException);
    scheduler.removeActionDriver("test-2");
  }

  @Test(timeout = 10000)
  public void testUncancellableOperator() throws Exception {
    ActionDriver driver = scheduler.getOrCreateActionDriver("test-3");
    Map<String, String> settings = new HashMap<>();
    settings.put("key", "blabla");
    SleepOperator sleepOperator = new SleepOperator();
    HelloOperator first = new HelloOperator();
    HelloOperator second = new HelloOperator();
    first.setRetryPolicy(new RetryPolicy()).setProperties(ImmutableMap.of(HelloOperator.MSG, "first"));
    second.setRetryPolicy(new RetryPolicy()).setProperties(ImmutableMap.of(HelloOperator.MSG, "second"));
    sleepOperator.onSuccess(first)
        .setProperties(ImmutableMap.of(SleepOperator.SLEEP_TIME_MS, "10000",
            SleepOperator.CHECK_CANCELLED, "false"))
        .onFailure(second)
        .setRetryPolicy(new RetryPolicy());
    Action action = Action.create("test-3", sleepOperator, settings);
    driver.submit(action);
    Thread.sleep(200);
    driver.cancel();
    driver.waitForFinish(1000000);
    Assert.assertEquals(action.getState(), ActionState.COMPLETED);
    Assert.assertEquals(first.getRetryPolicy().getAttemptCount(), 0);
    Assert.assertEquals(second.getRetryPolicy().getAttemptCount(), 1);
    Assert.assertEquals(sleepOperator.getRetryPolicy().getAttemptCount(), 1);

    Assert.assertEquals(first.getRetryPolicy().getLastFailure(), null);
    Assert.assertEquals(second.getRetryPolicy().getLastFailure(), null);
    Assert.assertTrue(sleepOperator.getRetryPolicy().getLastFailure() instanceof OperationCancelledException);
    scheduler.removeActionDriver("test-3");
  }

  @Test(timeout = 10000)
  public void testCancelOnceActionDriver() throws Exception {
    ActionDriver driver = scheduler.getOrCreateActionDriver("test-4");
    Map<String, String> settings = new HashMap<>();
    settings.put("key", "blabla");
    SleepOperator sleepOperator = new SleepOperator();
    HelloOperator first = new HelloOperator();
    HelloOperator second = new HelloOperator();
    first.setRetryPolicy(new RetryPolicy()).setProperties(ImmutableMap.of(HelloOperator.MSG, "first"));
    second.setRetryPolicy(new RetryPolicy()).setProperties(ImmutableMap.of(HelloOperator.MSG, "second"));
    sleepOperator.onSuccess(first)
        .setProperties(ImmutableMap.of(SleepOperator.SLEEP_TIME_MS, "10000",
            SleepOperator.CHECK_CANCELLED, "true"))
        .onFailure(second)
        .setRetryPolicy(new RetryPolicy());
    Action action1 = Action.create("test-1", sleepOperator, settings);
    driver.submit(action1);
    Thread.sleep(200);
    SleepOperator third = new SleepOperator();
    third.setRetryPolicy(new RetryPolicy())
        .setProperties(ImmutableMap.of(SleepOperator.SLEEP_TIME_MS, "300",
            SleepOperator.CHECK_CANCELLED, "false"));
    Action action2 = Action.create("test-2", third, settings);
    driver.cancelAndSubmitAction(action2);
    HelloOperator fourth = new HelloOperator();
    Action action3 = Action.create("test-3", fourth, settings);
    Assert.assertThrows(ServerBusyException.class, () -> driver.cancelAndSubmitAction(action3));
    driver.waitForFinish(1000000);
    Assert.assertEquals(action1.getState(), ActionState.COMPLETED);
    Assert.assertEquals(action2.getState(), ActionState.COMPLETED);
    Assert.assertEquals(first.getRetryPolicy().getAttemptCount(), 0);
    Assert.assertEquals(second.getRetryPolicy().getAttemptCount(), 1);
    Assert.assertEquals(sleepOperator.getRetryPolicy().getAttemptCount(), 1);
    Assert.assertEquals(third.getRetryPolicy().getAttemptCount(), 1);

    Assert.assertEquals(first.getRetryPolicy().getLastFailure(), null);
    Assert.assertEquals(second.getRetryPolicy().getLastFailure(), null);
    Assert.assertTrue(sleepOperator.getRetryPolicy().getLastFailure() instanceof OperationCancelledException);
    Assert.assertEquals(third.getRetryPolicy().getLastFailure(), null);
    scheduler.removeActionDriver("test-4");
  }

  @Test(timeout = 10000)
  public void testFailover() throws Exception {
    ActionScheduler schedulerSpy = Mockito.spy(scheduler);
    doNothing().when(schedulerSpy).schedule(any(Action.class));
    ActionDriver driver = schedulerSpy.getOrCreateActionDriver("test-5");
    Map<String, String> settings = new HashMap<>();
    settings.put("key", "blabla");
    SleepOperator sleepOperator = new SleepOperator();
    HelloOperator first = new HelloOperator();
    HelloOperator second = new HelloOperator();
    first.setRetryPolicy(new RetryPolicy()).setProperties(ImmutableMap.of(HelloOperator.MSG, "first"));
    second.setRetryPolicy(new RetryPolicy()).setProperties(ImmutableMap.of(HelloOperator.MSG, "second"));
    sleepOperator.onSuccess(first)
        .setProperties(ImmutableMap.of(SleepOperator.SLEEP_TIME_MS, "10000",
            SleepOperator.CHECK_CANCELLED, "true"))
        .onFailure(second)
        .setRetryPolicy(new RetryPolicy());
    Action action1 = Action.create("test-1", sleepOperator, settings).setIsPersistent(true);
    driver.submit(action1);
    HelloOperator third = new HelloOperator();
    third.setRetryPolicy(new RetryPolicy());
    Action action2 = Action.create("test-2", third, settings).setIsPersistent(true);
    driver.cancelAndSubmitAction(action2);
    Assert.assertEquals(metaClient.getActions("test-5").size(), 2);
    Thread.sleep(200);
    Assert.assertEquals(action1.getState(), ActionState.RUNNING);
    Assert.assertEquals(action2.getState(), ActionState.RUNNING);
    schedulerSpy.removeActionDriver("test-5");
    //begin failover
    ActionScheduler.CancelOnceActionDriver coaDriver =
        (ActionScheduler.CancelOnceActionDriver) schedulerSpy.getOrCreateActionDriver("test-5");
    doAnswer((Answer<Void>) invocation -> {
      Thread.sleep(100);
      scheduler.schedule(invocation.getArgument(0));
      return null;
    }).when(schedulerSpy).schedule(any(Action.class));
    coaDriver.recover();
    action1 = coaDriver.getNormalAction();
    sleepOperator = (SleepOperator) action1.getRoot();
    first = (HelloOperator) sleepOperator.getSuccessChild();
    second = (HelloOperator) sleepOperator.getFailureChild();
    action2 = coaDriver.getExclusiveAction();
    third = (HelloOperator) action2.getRoot();
    coaDriver.waitForFinish(1000000);
    Assert.assertEquals(action1.getState(), ActionState.COMPLETED);
    Assert.assertEquals(action2.getState(), ActionState.COMPLETED);
    Assert.assertEquals(first.getRetryPolicy().getAttemptCount(), 0);
    Assert.assertEquals(second.getRetryPolicy().getAttemptCount(), 1);
    Assert.assertEquals(sleepOperator.getRetryPolicy().getAttemptCount(), 1);
    Assert.assertEquals(third.getRetryPolicy().getAttemptCount(), 1);

    Assert.assertEquals(first.getRetryPolicy().getLastFailure(), null);
    Assert.assertEquals(second.getRetryPolicy().getLastFailure(), null);
    Assert.assertTrue(sleepOperator.getRetryPolicy().getLastFailure() instanceof OperationCancelledException);
    Assert.assertEquals(third.getRetryPolicy().getLastFailure(), null);
    scheduler.removeActionDriver("test-5");
  }
}
