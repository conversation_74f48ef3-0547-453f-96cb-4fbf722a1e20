package com.clickzetta.inventory.metric;

import io.prometheus.client.Collector;
import io.prometheus.client.Gauge;

public class InventoryMetrics {
  private static final Gauge InstanceUsageLevel =
      Gauge.build()
          .name("Lakehouse_InventoryManager_Instance_Usage_Level")
          .help("Inventory Instance Usage Level")
          .labelNames("inventory_instance")
          .register();

  private static final Gauge InstanceMaxLevel =
      Gauge.build()
          .name("Lakehouse_InventoryManager_Instance_Max_Level")
          .help("Inventory Instance Max Usage Level")
          .labelNames("inventory_instance")
          .register();

  private static final Gauge InstanceMinLevel =
      Gauge.build()
          .name("Lakehouse_InventoryManager_Instance_Min_Level")
          .help("Inventory Instance Min Usage Level")
          .labelNames("inventory_instance")
          .register();

  private static final Gauge InstanceTargetLevel =
      Gauge.build()
          .name("Lakehouse_InventoryManager_Instance_Target_Level")
          .help("Inventory Instance Target Usage Level")
          .labelNames("inventory_instance")
          .register();

  private static final Gauge InstanceTotalPodCount =
      Gauge.build()
          .name("Lakehouse_InventoryManager_Instance_Total_Slot_Count")
          .help("Inventory Instance Total Slot Count")
          .labelNames("inventory_instance")
          .register();

  private static final Gauge InstanceUsedPodCount =
      Gauge.build()
          .name("Lakehouse_InventoryManager_Used_Slot_Count")
          .help("Inventory Instance Used Slot Count")
          .labelNames("inventory_instance")
          .register();

  private static final Gauge InstanceBusyPodCount =
      Gauge.build()
          .name("Lakehouse_InventoryManager_Busy_Slot_Count")
          .help("Inventory Instance Busy Slot Count")
          .labelNames("inventory_instance")
          .register();

  private static final Gauge InstanceCurrentPodCount =
      Gauge.build()
          .name("Lakehouse_InventoryManager_Current_Slot_Count")
          .help("Inventory Instance Busy Slot Count")
          .labelNames("inventory_instance", "type")
          .register();

  private static final Gauge InstanceMaxPodCount =
      Gauge.build()
          .name("Lakehouse_InventoryManager_Max_Slot_Count")
          .help("Inventory Instance Busy Slot Count")
          .labelNames("inventory_instance")
          .register();

  private static final Gauge InstanceScalingFailureCount =
      Gauge.build()
          .name("Lakehouse_InventoryManager_Instance_Scaling_Failure_Count")
          .help("Inventory Instance Scaling Failure Count")
          .labelNames("inventory_instance", "method")
          .register();

  private static final Gauge InstanceReportCount =
      Gauge.build()
          .name("Lakehouse_InventoryManager_Instance_Report_Count")
          .help("Inventory Instance Report Count")
          .labelNames("inventory_instance")
          .register();

  private static final Gauge InstanceEffectiveCount =
      Gauge.build()
          .name("Lakehouse_InventoryManager_Instance_Effective_Count")
          .help("Inventory Instance Effective Count")
          .labelNames("inventory_instance")
          .register();

  private static final Gauge InstanceGuaranteedPodCount =
      Gauge.build()
          .name("Lakehouse_InventoryManager_Instance_Guaranteed_Slot_Count")
          .help("Inventory Instance Guaranteed Slot Count")
          .labelNames("inventory_instance")
          .register();

  public static void setInstanceUsageLevel(String inventory_instance, double instanceUsageLevel) {
    InstanceUsageLevel.labels(inventory_instance).set(instanceUsageLevel);
  }

  public static void setInstanceMaxLevel(String inventory_instance, float maxLevel) {
    InstanceMaxLevel.labels(inventory_instance).set(maxLevel);
  }

  public static void setInstanceMinLevel(String inventory_instance, float minLevel) {
    InstanceMinLevel.labels(inventory_instance).set(minLevel);
  }

  public static void setInstanceTargetLevel(String inventory_instance, float targetLevel) {
    InstanceTargetLevel.labels(inventory_instance).set(targetLevel);
  }

  public static void setInstanceTotalSlots(String inventory_instance, long total) {
    InstanceTotalPodCount.labels(inventory_instance).set(total);
  }

  public static void setInstanceUsedSlots(String inventory_instance, double used) {
    InstanceUsedPodCount.labels(inventory_instance).set(used);
  }

  public static void setInstanceBusySlots(String inventory_instance, long busy) {
    InstanceBusyPodCount.labels(inventory_instance).set(busy);
  }

  public static void setInstanceCurrentSlots(String inventory_instance, String type, int current) {
    InstanceCurrentPodCount.labels(inventory_instance, type).set(current);
  }

  public static void setInstanceMaxSlots(String inventory_instance, int max) {
    InstanceMaxPodCount.labels(inventory_instance).set(max);
  }

  public static void incInstanceScalingFailureCount(String inventory_instance, String method) {
    InstanceScalingFailureCount.labels(inventory_instance, method).inc();
  }

  public static void incInstanceReportCount(String inventory_instance) {
    InstanceReportCount.labels(inventory_instance).inc();
  }

  public static void incInstanceEffectiveCount(String inventory_instance) {
    InstanceEffectiveCount.labels(inventory_instance).inc();
  }

  public static void setInstanceGuaranteedSlots(String inventory_instance, long guaranteed) {
    InstanceGuaranteedPodCount.labels(inventory_instance).set(guaranteed);
  }

  public static void removeInstance(String inventory_instance) {
    InstanceUsageLevel.remove(inventory_instance);
    InstanceMaxLevel.remove(inventory_instance);
    InstanceMinLevel.remove(inventory_instance);
    InstanceTargetLevel.remove(inventory_instance);
    InstanceTotalPodCount.remove(inventory_instance);
    InstanceUsedPodCount.remove(inventory_instance);
    InstanceBusyPodCount.remove(inventory_instance);
    InstanceMaxPodCount.remove(inventory_instance);
    InstanceReportCount.remove(inventory_instance);
    InstanceEffectiveCount.remove(inventory_instance);
    InstanceGuaranteedPodCount.remove(inventory_instance);
    for (Collector.MetricFamilySamples.Sample sample :
        InstanceScalingFailureCount.collect().get(0).samples) {
      if (sample.labelValues.get(0).equals(inventory_instance)) {
        InstanceScalingFailureCount.remove(sample.labelValues.get(0), sample.labelValues.get(1));
      }
    }
    for (Collector.MetricFamilySamples.Sample sample :
        InstanceCurrentPodCount.collect().get(0).samples) {
      if (sample.labelValues.get(0).equals(inventory_instance)) {
        InstanceCurrentPodCount.remove(sample.labelValues.get(0), sample.labelValues.get(1));
      }
    }
  }
}
