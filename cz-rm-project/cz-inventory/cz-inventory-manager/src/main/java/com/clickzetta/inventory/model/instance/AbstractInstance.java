package com.clickzetta.inventory.model.instance;

import com.clickzetta.im.proto.InventoryManagerAdminServiceProtos;
import com.clickzetta.inventory.InventoryContext;
import com.clickzetta.inventory.common.InventoryConfiguration;
import com.clickzetta.inventory.metric.InventoryMetrics;
import com.clickzetta.provider.kube.KubeNodeMeta;
import com.clickzetta.rm.common.CZConfiguration;
import com.clickzetta.rm.common.api.inventory.FrameworkType;
import com.clickzetta.rm.common.api.inventory.InstanceAction;
import com.clickzetta.rm.common.api.inventory.InstanceReport;
import com.clickzetta.rm.common.api.inventory.InstanceState;
import com.clickzetta.rm.common.api.inventory.PodState;
import com.clickzetta.rm.common.api.inventory.PodStatus;
import com.clickzetta.rm.common.exception.ExceptionWithErrorCode;
import com.clickzetta.rm.common.exception.InternalException;
import com.clickzetta.rm.common.exception.InvalidParameterException;
import com.clickzetta.rm.common.exception.RetryableException;
import com.clickzetta.rm.common.utils.InventoryUtils;
import com.clickzetta.rm.scheduler.ActionDriver;
import com.clickzetta.rm.scheduler.ActionUtils;
import com.google.common.collect.ImmutableMap;
import io.kubernetes.client.openapi.ApiException;
import lombok.Getter;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.yarn.api.records.Resource;
import org.apache.hadoop.yarn.event.EventHandler;
import org.apache.hadoop.yarn.proto.YarnProtos;
import org.apache.hadoop.yarn.state.MultipleArcTransition;
import org.apache.hadoop.yarn.state.SingleArcTransition;
import org.apache.hadoop.yarn.state.StateMachine;
import org.apache.hadoop.yarn.state.StateMachineFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

public abstract class AbstractInstance implements EventHandler<InstanceEvent> {
  private static final Logger LOG = LoggerFactory.getLogger(AbstractInstance.class);
  protected volatile InventoryConfiguration.FrameworkInstanceConfig volatileConfig;
  @Getter
  protected InventoryContext context;
  private final ReentrantReadWriteLock.ReadLock readLock;
  private final ReentrantReadWriteLock.WriteLock writeLock;
  protected ActionDriver actionDriver;
  @Getter
  protected final int actionRetryIntervalMs;
  @Getter
  protected final int maxActionRetryMs;
  @Getter
  protected final int maxReimbursableActionRetryMs;
  @Getter
  protected final int maxDrainRetryMs;
  protected final FrameworkType frameworkType;
  protected final String name;
  @Getter
  protected UsageLevelController usageLevelController;
  protected AtomicReference<InstanceReport> latestReport = new AtomicReference<>();
  protected Map<String, String> podIdx = new HashMap<>(); //pod name -> node name, used only for draining
  protected Map<String, String> drainingPods = new HashMap<>(); //pod name -> node name
  protected Map<String, ImNode> drainingNodes = new HashMap<>(); //node name -> node
  protected Set<String> podsToUndrain = new HashSet<>();
  protected Map<String, KubeNodeMeta> nodesToScaleIn = new HashMap<>();
  protected Set<String> podsToScaleIn = new HashSet<>();
  protected long lastStateTransitionTime = -1;
  protected AtomicBoolean deleting = new AtomicBoolean(false);
  protected long unBalanceTime = 0;

  protected static StateMachineFactory<AbstractInstance, InstanceState, InstanceEventType, InstanceEvent>
      stateMachineFactory =
      new StateMachineFactory<AbstractInstance, InstanceState, InstanceEventType, InstanceEvent>(
          InstanceState.RUNNING)
          .addTransition(InstanceState.RUNNING, EnumSet.of(InstanceState.SCALING_OUT, InstanceState.RUNNING),
              InstanceEventType.PREPARE_SCALE_OUT, new PrepareScaleOutTransition())
          .addTransition(InstanceState.RUNNING, EnumSet.of(InstanceState.SCALING_IN, InstanceState.RUNNING),
              InstanceEventType.PREPARE_SCALE_IN, new PrepareScaleInTransition())
          .addTransition(InstanceState.RUNNING, EnumSet.of(InstanceState.RUNNING, InstanceState.DROPPED),
              InstanceEventType.PREPARE_DROP, new PrepareDropTransition())
          .addTransition(InstanceState.RUNNING, EnumSet.of(InstanceState.RUNNING, InstanceState.SCALING_OUT,
              InstanceState.SCALING_IN), InstanceEventType.PREPARE_BALANCE, new PrepareBalanceTransition())
          .addTransition(InstanceState.SCALING_OUT, InstanceState.RUNNING, InstanceEventType.SCALE_OUT_SUCCESS)
          .addTransition(InstanceState.SCALING_OUT, InstanceState.RUNNING, InstanceEventType.SCALE_OUT_ERROR)
          .addTransition(InstanceState.SCALING_IN, InstanceState.RUNNING, InstanceEventType.SCALE_IN_SUCCESS)
          .addTransition(InstanceState.SCALING_IN, InstanceState.RUNNING, InstanceEventType.SCALE_IN_ERROR,
              new ScaleInInstanceErrorTransition())
          .installTopology();

  protected StateMachine<InstanceState, InstanceEventType, InstanceEvent> stateMachine;

  public AbstractInstance(InventoryContext context, FrameworkType frameworkType,
                          InventoryConfiguration.FrameworkInstanceConfig instanceConfig) {
    this.context = context;
    this.volatileConfig = instanceConfig;
    ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    this.readLock = lock.readLock();
    this.writeLock = lock.writeLock();
    this.frameworkType = frameworkType;
    this.name = InventoryUtils.formatInstanceId(frameworkType, instanceConfig.getVersion());
    this.actionDriver = context.getActionScheduler().getOrCreateActionDriver(
        ActionUtils.getInventoryFrameworkInstanceDriverId(frameworkType, instanceConfig.getVersion()));
    this.actionDriver.setHolder(this);
    this.actionRetryIntervalMs = context.getConfiguration().getInt(CZConfiguration.INSTANCE_ACTION_RETRY_INTERVAL_MS,
        CZConfiguration.DEFAULT_INSTANCE_ACTION_RETRY_INTERVAL_MS);
    this.maxActionRetryMs = context.getConfiguration().getInt(CZConfiguration.INSTANCE_ACTION_MAX_RETRY_MS,
        CZConfiguration.DEFAULT_INSTANCE_ACTION_MAX_RETRY_MS);
    this.maxReimbursableActionRetryMs = context.getConfiguration().getInt(CZConfiguration.INSTANCE_REIMBURSABLE_ACTION_MAX_RETRY_MS,
        CZConfiguration.DEFAULT_INSTANCE_REIMBURSABLE_ACTION_MAX_RETRY_MS);
    this.maxDrainRetryMs = context.getConfiguration().getInt(CZConfiguration.INSTANCE_DRAIN_MAX_RETRY_MS,
        CZConfiguration.DEFAULT_INSTANCE_DRAIN_MAX_RETRY_MS);
    this.usageLevelController = new UsageLevelController(name,
        instanceConfig.getMinUsedThreshold(),
        instanceConfig.getMaxUsedThreshold(),
        instanceConfig.getTargetUsedThreshold(),
        instanceConfig.getScaleOutDelayMs(),
        instanceConfig.getScaleInDelayMs(),
        instanceConfig.getPendingDelayMs(),
        Resource.newInstance(instanceConfig.getPodResourceMem(), instanceConfig.getPodResourceVCores()),
        context.getResourceCalculator());
    this.stateMachine = stateMachineFactory.make(this, InstanceState.RUNNING);
    lastStateTransitionTime = System.currentTimeMillis();
  }

  public String getName() {
    return name;
  }

  public void monitor() {
    InventoryConfiguration.FrameworkInstanceConfig instanceConfig = volatileConfig;
    try {
      if (getState() == InstanceState.RUNNING) {
        Map<YarnProtos.NodeType, Integer> desiredSize = getDesiredPodSize(instanceConfig);
        Map<YarnProtos.NodeType, Integer> readySize = getReadyPodSize(instanceConfig);
        Map<YarnProtos.NodeType, Integer> activePodSize = getElasticActivePodSize();
        for (Map.Entry<YarnProtos.NodeType, InventoryConfiguration.NodeTypeConfig> conf :
            instanceConfig.getNodeType().entrySet()) {
          int poolReadySize = readySize.getOrDefault(conf.getKey(), 0);
          int poolActivePodSize = activePodSize.getOrDefault(conf.getKey(), 0);
          if (poolReadySize != poolActivePodSize) {
            LOG.warn("Instance {} type {} ready pod size {} is not equal to active pod size from report {}",
                name, conf.getKey(), poolReadySize, poolActivePodSize);
          }
        }
        if (readySize.equals(desiredSize) && deleting.get()) {
          tryDelete();
        } else if (checkBalance(instanceConfig)) {
          balanceNodePool();
        }
      }
    } catch (Throwable t) {
      LOG.error("Error monitoring instance " + name, t);
    }
  }

  private boolean checkBalance(InventoryConfiguration.FrameworkInstanceConfig instanceConfig) throws ExceptionWithErrorCode {
    Map<YarnProtos.NodeType, Integer> currentSize = getDesiredPodSize(instanceConfig);
    int totalSize = currentSize.values().stream().mapToInt(Integer::intValue).sum();
    if (totalSize == 0) {
      unBalanceTime = 0;
      return false;
    }

    Map<YarnProtos.NodeType, Integer> balancedSize = InventoryUtils.getSizeByPool(totalSize,
        getTypeAndRatio(instanceConfig));
    float diffRatio = 0.0F;
    for (Map.Entry<YarnProtos.NodeType, Integer> current : currentSize.entrySet()) {
      diffRatio += 1.0F * Math.abs(current.getValue() - balancedSize.getOrDefault(current.getKey(), 0)) / totalSize;
    }
    if (diffRatio <= instanceConfig.getUnbalanceToleranceRatio()) {
      LOG.debug("Instance {} balanced, diff ratio {}", name, diffRatio);
      unBalanceTime = 0;
      return false;
    }

    long now = System.currentTimeMillis();
    if (unBalanceTime == 0) {
      unBalanceTime = now;
      return false;
    }

    if (now - unBalanceTime < instanceConfig.getBalanceDelayMs()) {
      return false;
    }
    return true;
  }

  @Override
  public void handle(InstanceEvent event) {
    try {
      handleEventInterval(event);
    } catch (Throwable e) {
      LOG.error("Error handling event type " + event.getType() + " for instance " + name, e);
    }
  }

  public InventoryConfiguration.FrameworkInstanceConfig instanceConfig() {
    return volatileConfig;
  }

  protected void handleEventInterval(InstanceEvent event) throws ExceptionWithErrorCode {
    LOG.debug("Processing {} of type {}", name, event.getType());
    writeLock.lock();
    try {
      InstanceState oldState = stateMachine.getCurrentState();
      stateMachine.doTransition(event.getType(), event);
      InstanceState newState = stateMachine.getCurrentState();
      if (oldState != newState) {
        lastStateTransitionTime = System.currentTimeMillis();
        LOG.info("Instance " + this.name + " transitioned from " + oldState + " to "
            + newState);
      }
    } catch (RuntimeException e) {
      if (e.getCause() instanceof ExceptionWithErrorCode) {
        throw (ExceptionWithErrorCode) e.getCause();
      } else {
        throw e;
      }
    } finally {
      writeLock.unlock();
    }
  }

  public void scaleOut(int targetSize,
                       boolean allOrNothing,
                       InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode {
    PrepareScaleOutInstanceEvent event = new PrepareScaleOutInstanceEvent(targetSize, allOrNothing, instanceConfig);
    handleEventInterval(event);
  }

  public void balanceNodePool()
      throws ExceptionWithErrorCode {
    InstanceEvent event = new InstanceEvent(InstanceEventType.PREPARE_BALANCE);
    handleEventInterval(event);
  }


  public void scaleIn(int targetSize,
                      InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode {
    PrepareScaleInInstanceEvent event = new PrepareScaleInInstanceEvent(targetSize, instanceConfig);
    handleEventInterval(event);
  }

  public InstanceState getState() {
    readLock.lock();
    try {
      return stateMachine.getCurrentState();
    } finally {
      readLock.unlock();
    }
  }

  private Map<YarnProtos.NodeType, Float> getTypeAndRatio(InventoryConfiguration.FrameworkInstanceConfig instanceConfig) {
    Map<YarnProtos.NodeType, Float> typeAndRatio = new HashMap<>();
    for (Map.Entry<YarnProtos.NodeType, InventoryConfiguration.NodeTypeConfig> conf : instanceConfig.getNodeType().entrySet()) {
      typeAndRatio.put(conf.getKey(), conf.getValue().getRatio());
    }
    return typeAndRatio;
  }

  protected InstanceState prepareScaleOut(int targetSize,
                                          boolean allOrNothing,
                                          InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode {
    LOG.info("Preparing to scale out instance " + name + " to " + targetSize + " replicas");
    int totalActivePodSize = getTotalActivePodSize();
    if (totalActivePodSize >= targetSize) {
      LOG.info("Instance " + name + " already has " + totalActivePodSize + " replicas");
      return InstanceState.RUNNING;
    }

    Map<YarnProtos.NodeType, Integer> readyElasticSize = getReadyPodSize(instanceConfig);
    int totalReservedPodSize =
        totalActivePodSize - readyElasticSize.values().stream().mapToInt(Integer::intValue).sum();
    if (totalReservedPodSize >= targetSize) {
      LOG.info("Instance " + name + " already has " + totalReservedPodSize + " reserved replicas");
      return InstanceState.RUNNING;
    }

    Map<YarnProtos.NodeType, Integer> elasticTarget =
        InventoryUtils.getSizeByPool(targetSize - totalReservedPodSize, getTypeAndRatio(instanceConfig));
    Map<YarnProtos.NodeType, Integer> normalizedElasticTarget = normalizeTargetSize(elasticTarget, instanceConfig);
    LOG.info("Prepare scale out instance {}, ready size: {}, pods to create: {}, active pod size: {}," +
            "total target size: {}, elastic target size: {}, normalized elastic target size: {}",
        name, readyElasticSize, targetSize - totalActivePodSize, totalActivePodSize, targetSize, elasticTarget,
        normalizedElasticTarget);

    int totalTarget = normalizedElasticTarget.values().stream().mapToInt(Integer::intValue).sum();
    if (allOrNothing && totalTarget < targetSize - totalReservedPodSize) {
      LOG.info("All or nothing is true, target size is not reached, skip scale out, instance {} pool {}", name,
          totalTarget);
      return InstanceState.RUNNING;
    }
    Iterator<Map.Entry<YarnProtos.NodeType, Integer>> iter = normalizedElasticTarget.entrySet().iterator();
    while (iter.hasNext()) {
      Map.Entry<YarnProtos.NodeType, Integer> entry = iter.next();
      if (readyElasticSize.get(entry.getKey()) >= entry.getValue()) {
        LOG.info("Instance {} pool {} already has {} elastic replicas", name, entry.getKey(),
            readyElasticSize.get(entry.getKey()));
        iter.remove();
      }
    }
    if (normalizedElasticTarget.isEmpty()) {
      return InstanceState.RUNNING;
    }
    actionDriver.submit(ScaleOutInstanceAction.create(this, totalActivePodSize,
        totalTarget, normalizedElasticTarget, true,
        instanceConfig));
    return InstanceState.SCALING_OUT;
  }

  /*
   * return node to pods map
   */
  protected abstract Map<Pair<YarnProtos.NodeType, String>, Set<String>> getPodsByNodeInternal(
      InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode;

  protected Map<Pair<YarnProtos.NodeType, String>, Set<String>> getPodsByNode(
      InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode {
    Map<Pair<YarnProtos.NodeType, String>, Set<String>> podsByNodes =
        getPodsByNodeInternal(instanceConfig);
    podIdx.clear();
    for (Map.Entry<Pair<YarnProtos.NodeType, String>, Set<String>> entry :
        podsByNodes.entrySet()) {
      String nodeName = entry.getKey().getValue();
      Set<String> podNames = entry.getValue();
      for (String podName : podNames) {
        podIdx.put(podName, nodeName);
      }
    }
    return podsByNodes;
  }

  protected void onScaledOut(int scaledSize) {
    if (scaledSize > 0) {
      LOG.info("Instance {} scaled out {} replicas", name, scaledSize);
      usageLevelController.resetPendingQueue();
      usageLevelController.resetAbnormalUsageQueue();
    }
  }

  protected void onScaledIn(int scaledSize) {
    if (scaledSize > 0) {
      LOG.info("Instance {} scaled in {} replicas", name, scaledSize);
      usageLevelController.resetAbnormalUsageQueue();
    }
  }

  //key: node type
  protected Map<YarnProtos.NodeType, TreeSet<ImNode>> getNodeLoads(InstanceReport report,
                                                      InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode {
    Map<Pair<YarnProtos.NodeType, String>, Set<String>> podsByNodes =
        getPodsByNode(instanceConfig);
    Map<YarnProtos.NodeType, TreeSet<ImNode>> nodesByPool = new HashMap<>();
    for (Map.Entry<Pair<YarnProtos.NodeType, String>, Set<String>> entry :
        podsByNodes.entrySet()) {
      TreeSet<ImNode> nodes = nodesByPool.computeIfAbsent(entry.getKey().getKey(), k -> new TreeSet<>());
      String nodeName = entry.getKey().getValue();
      Set<String> podNames = entry.getValue();
      ImNode node = new ImNode(nodeName);
      node.setNodeType(entry.getKey().getKey());
      try {
        node.setNodeInfo(context.getKubeClient().getNodeInfo(nodeName));
      } catch (ApiException e) {
        LOG.warn("Failed to get node info for node {}, code: {}, body: {}, headers: {}",
            nodeName, e.getCode(), e.getResponseBody(), e.getResponseHeaders(), e);
        throw new InternalException(e);
      }
      node.getKubePodNames().addAll(podNames);
      for (String podName : podNames) {
        PodStatus podStatus = report.getPodMap().get(podName);
        if (podStatus != null) {
          node.addPodLoad(podStatus);
        } else {
          LOG.warn("Pod {} not found in instance {} report", podName, name);
        }
      }
      nodes.add(node);
    }
    return nodesByPool;
  }

  protected void judgeCandidatePods2Drain(int targetSize, InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode {
    InstanceReport report = latestReport.get();
    if (report == null) {
      LOG.warn("No report for instance {} scaling in", name);
      return;
    }
    int totalActivePodSize = getTotalActivePodSize();
    if (totalActivePodSize <= targetSize) {
      LOG.info("No need to scale in instance {}, current alive size: {}, target size: {}",
          name, totalActivePodSize, targetSize);
      return;
    }
    // still get pod by node loads try best (maybe inaccurate when pods of multi types on node), thus k8s
    // could scale in node ASAP (drain/migrate pod etc.)
    Map<YarnProtos.NodeType, TreeSet<ImNode>> nodesByPool = getNodeLoads(report, instanceConfig);
    LOG.info("Node loads for instance {} are {}", name, nodesByPool);
    Map<YarnProtos.NodeType, Integer> activePodSize = new HashMap<>();
    for (Map.Entry<YarnProtos.NodeType, TreeSet<ImNode>> nodes : nodesByPool.entrySet()) {
      activePodSize.put(nodes.getKey(), nodes.getValue().stream().mapToInt(ImNode::getAlivePods).sum());
    }
    int totalReservedPodSize =
        totalActivePodSize - getElasticActivePodSize().values().stream().mapToInt(Integer::intValue).sum();
    int elasticTargetSize = Math.max(0, targetSize - totalReservedPodSize);
    Map<YarnProtos.NodeType, Integer> curTargetSize = InventoryUtils.getSizeByPool(elasticTargetSize,
        getTypeAndRatio(instanceConfig));
    Map<YarnProtos.NodeType, Integer> podsToDelete = new HashMap<>();
    int shortage = 0;
    int enoughCount = 0;
    for (Map.Entry<YarnProtos.NodeType, TreeSet<ImNode>> nodes : nodesByPool.entrySet()) {
      int poolActiveSize = activePodSize.getOrDefault(nodes.getKey(), 0);
      int poolTargetSize = curTargetSize.get(nodes.getKey());
      if (poolActiveSize > poolTargetSize) {
        podsToDelete.put(nodes.getKey(), poolActiveSize - poolTargetSize);
        enoughCount++;
      } else if (poolTargetSize > poolActiveSize) {
        shortage += poolTargetSize - poolActiveSize;
        LOG.info("Instance {} pool {} NOT has enough for target size {}, current active {}", name, nodes.getKey(),
            poolTargetSize, poolActiveSize);
      }
    }
    if (shortage > 0 && enoughCount > 0) {
      int perReimburse = shortage / enoughCount;
      for (Map.Entry<YarnProtos.NodeType, TreeSet<ImNode>> nodes : nodesByPool.entrySet()) {
        int poolActiveSize = activePodSize.getOrDefault(nodes.getKey(), 0);
        int poolTargetSize = curTargetSize.get(nodes.getKey());
        if (poolActiveSize > poolTargetSize) {
          if (poolActiveSize - poolTargetSize > perReimburse) {
            podsToDelete.put(nodes.getKey(), poolActiveSize - poolTargetSize - perReimburse);
            LOG.info("Instance {} pool {} to delete pod reimburse from {} to {}", name, nodes.getKey(),
                poolActiveSize - poolTargetSize, poolActiveSize - poolTargetSize - perReimburse);
          } else {
            podsToDelete.remove(nodes.getKey());
            LOG.info("Instance {} pool {} erase from to delete pod {} vs {}", name, nodes.getKey(),
                poolActiveSize - poolTargetSize, perReimburse);
          }
        }
      }
    }
    if (podsToDelete.isEmpty()) {
      LOG.info("No need to scale in instance {} after reimburse", name);
    } else {
      judgeCandidatePods2DrainInternal(podsToDelete, nodesByPool, instanceConfig);
    }
  }

  protected void judgeCandidatePods2Drain(Map<YarnProtos.NodeType, Integer> podsToDelete,
                                          InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode {
    InstanceReport report = latestReport.get();
    if (report == null) {
      LOG.warn("No report for instance {} scaling in", name);
      return;
    }
    Map<YarnProtos.NodeType, TreeSet<ImNode>> nodesByPool = getNodeLoads(report, instanceConfig);
    LOG.info("Node loads for instance {} are {}", name, nodesByPool);
    judgeCandidatePods2DrainInternal(podsToDelete, nodesByPool, instanceConfig);
  }

  protected void judgeCandidatePods2DrainInternal(Map<YarnProtos.NodeType, Integer> podsToDelete,
                                                  Map<YarnProtos.NodeType, TreeSet<ImNode>> nodesByPool,
                                                  InventoryConfiguration.FrameworkInstanceConfig instanceConfig) throws ExceptionWithErrorCode {
    LOG.info("Instance {} pods to delete {}", name, podsToDelete);
    drainingNodes.clear();
    drainingPods.clear();
    podsToUndrain.clear();
    podsToScaleIn.clear();
    Map<YarnProtos.NodeType, Integer> targetSizeByType = new HashMap<>();
    Map<YarnProtos.NodeType, Integer> readySizeByType = new HashMap<>();
    for (Map.Entry<YarnProtos.NodeType, Integer> toDel : podsToDelete.entrySet()) {
      //int readySize = readySizeByType.getOrDefault(toDel.getKey(), 0);
      Set<ImNode> nodes = nodesByPool.get(toDel.getKey());
      int readySize = nodes.stream()
          .map(ImNode::getAlivePods)
          .mapToInt(Integer::intValue).sum();
      targetSizeByType.put(toDel.getKey(),
          readySize <= toDel.getValue() ? 0 : readySize - toDel.getValue());
      readySizeByType.put(toDel.getKey(), readySize);
    }

    Map<YarnProtos.NodeType, Integer> normalizedTargetSize = normalizeTargetSize(targetSizeByType, instanceConfig);
    for (Map.Entry<YarnProtos.NodeType, Integer> target : normalizedTargetSize.entrySet()) {
      if (!podsToDelete.containsKey(target.getKey())) {
        continue;
      }
      int normalizedPodsToDelete = readySizeByType.get(target.getKey()) - target.getValue();
      LOG.info("Prepare scale in instance {} type {}, ready size: {}, pods to delete: {}, normalized target size {}, " +
              "normalized pods to delete: {}, total active pods: {}",
          name, target.getKey(), readySizeByType.get(target.getKey()), podsToDelete.get(target.getKey()),
          target.getValue(),
          normalizedPodsToDelete,
          targetSizeByType.get(target.getKey()));

      if (normalizedPodsToDelete <= 0) {
        LOG.info("No pods to drain for instance {}, pods to delete: {}", name, normalizedPodsToDelete);
        continue;
      }
      for (ImNode node : nodesByPool.get(target.getKey())) {
        if (normalizedPodsToDelete <= 0) {
          break;
        }
        LOG.info("Node {} is a candidate to drain", node.getNodeName());
        if (!instanceConfig.partialDrain() && node.getAlivePods() > normalizedPodsToDelete) {
          LOG.info("Node {} has {} alive pods which is more than {}",
              node.getNodeName(), node.getAlivePods(), normalizedPodsToDelete);
          break;
        }
        drainingNodes.put(node.getNodeName(), node);
        for (PodStatus podStatus : node.getPodLoads()) {
          if (normalizedPodsToDelete == 0) {
            break;
          }
          if (!podStatus.getState().isAlive()) {
            LOG.info("Pod {} is not alive, skip", podStatus.getPodName());
            continue;
          }
          LOG.info("Pod {} is a candidate to drain", podStatus.getPodName());
          drainingPods.put(podStatus.getPodName(), node.getNodeName());
          normalizedPodsToDelete--;
        }
      }
      if (normalizedPodsToDelete > 0) {
        LOG.warn("Not enough pods to drain for instance {} pool {}, left pods to delete: {}", name, target.getKey(),
            normalizedPodsToDelete);
      }
    }
  }

  private InstanceState checkAndSubmitScaleInAction(InventoryConfiguration.FrameworkInstanceConfig instanceConfig) throws ExceptionWithErrorCode {
    if (drainingPods.isEmpty() || drainingNodes.isEmpty()) {
      drainingNodes.clear();
      drainingPods.clear();
      LOG.info("No pods to drain for instance {}", name);
      return InstanceState.RUNNING;
    }
    LOG.info("Preparing to scale in instance {}, Draining pods: {}, draining nodes: {}",
        name, drainingPods, drainingNodes);
    actionDriver.submit(ScaleInInstanceAction.create(this, instanceConfig));
    return InstanceState.SCALING_IN;
  }

  protected InstanceState prepareScaleIn(int targetSize,
                                         InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode {
    LOG.info("Preparing to scale in instance " + name + " to " + targetSize + " replicas");
    judgeCandidatePods2Drain(targetSize, instanceConfig);
    return checkAndSubmitScaleInAction(instanceConfig);
  }

  private Map<YarnProtos.NodeType, Integer> normalizeTargetSize(Map<YarnProtos.NodeType, Integer> targetSize,
                                                   InventoryConfiguration.FrameworkInstanceConfig instanceConfig) {
    int podsPerNode = Math.max(1, instanceConfig.getPodsPerNode());
    Map<YarnProtos.NodeType, Integer> normalizedSize = new HashMap<>();
    for (Map.Entry<YarnProtos.NodeType, Integer> target : targetSize.entrySet()) {
      int curPoolTargetSize = targetSize.get(target.getKey());
      int factor = curPoolTargetSize % podsPerNode == 0 ? 0 : 1;
      InventoryConfiguration.NodeTypeConfig nodeTypeConfig = instanceConfig.getNodeType().get(target.getKey());
      int res = Math.min((curPoolTargetSize / podsPerNode + factor) * podsPerNode,
          nodeTypeConfig.getMaxPodCount());
      normalizedSize.put(target.getKey(), Math.max(res, nodeTypeConfig.getMinPodCount()));
    }
    return normalizedSize;
  }

  /**
   * Check if the target size is valid and modify the target size if necessary
   *
   * @param targetSize target node count
   * @param inOrOut    true: scale in, false: scale out
   * @throws ExceptionWithErrorCode
   */
  abstract void checkAndModifyTargetSize(Map<YarnProtos.NodeType, Integer> targetSize,
                                          boolean inOrOut,
                                          InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode;

  //get ready pods size in kubernetes
  public abstract Map<YarnProtos.NodeType, Integer> getReadyPodSize(InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode;

  //get desired pods size in kubernetes
  public abstract Map<YarnProtos.NodeType, Integer> getDesiredPodSize(InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode;

  //get active pods size from instance report
  public int getTotalActivePodSize() {
    InstanceReport report = latestReport.get();
    if (report == null) {
      return 0;
    }
    return (int) report.getPodMap().values().stream()
        .filter(pod -> pod.getState().isAlive()).count();
  }

  public Map<YarnProtos.NodeType, Integer> getElasticActivePodSize() {
    InstanceReport report = latestReport.get();
    if (report == null) {
      return ImmutableMap.of();
    }
    Map<YarnProtos.NodeType, Integer> podSize = new HashMap<>();
    for (Map.Entry<YarnProtos.NodeType, InventoryConfiguration.NodeTypeConfig> conf : volatileConfig.getNodeType().entrySet()) {
      podSize.put(conf.getKey(), (int) report.getPodMap().values().stream()
          .filter(pod -> pod.getState().isAlive() && pod.getPodName().startsWith(conf.getValue().getKubeResName()))
          .count());
    }
    return podSize;
  }

  protected InstanceStatistics getStatistics(InventoryConfiguration.FrameworkInstanceConfig instanceConfig) {
    InstanceStatistics statistics = new InstanceStatistics();
    int unmanagedPods = 0;
    InstanceReport report = latestReport.get();
    //unmanaged pods
    if (report != null) {
      int total = 0;
      for (Map.Entry<YarnProtos.NodeType, InventoryConfiguration.NodeTypeConfig> conf :
          instanceConfig.getNodeType().entrySet()) {
        int podCnt = (int) report.getPodMap().values().stream()
            .filter(pod -> pod.getState().isAlive() && pod.getPodName().startsWith(conf.getValue().getKubeResName()))
            .count();
        total += podCnt;
        if (conf.getKey().equals(YarnProtos.NodeType.SPOT)) {
          statistics.setCurrentSpotPodCount(podCnt);
        }
      }
      unmanagedPods =
          (int) (report.getPodMap().values().stream().filter(pod -> pod.getState().isAlive()).count() - total);
    }
    statistics.setCurrentReservedPodCount(unmanagedPods);

    //max pod count
    int maxSize = 0;
    for (Map.Entry<YarnProtos.NodeType, InventoryConfiguration.NodeTypeConfig> conf : instanceConfig.getNodeType().entrySet()) {
      maxSize += conf.getValue().getMaxPodCount();
    }
    int maxPodCount = unmanagedPods + maxSize;
    statistics.setMaxPodCount(maxPodCount);

    if (report == null) {
      return statistics;
    }

    //current used pod count
    Map<String, PodStatus> pods = report.getPodMap();
    double used = 0;
    if (instanceConfig.getUsageLevelCalcMode() == InventoryConfiguration.UsageLevelCalcMode.UTILIZATION) {
      double utilization = 0;
      for (PodStatus status : pods.values()) {
        utilization += status.getUtilization();
      }
      used = utilization;
    } else {
      used = pods.values().stream()
          .filter(pod -> pod.getState().isInUse())
          .count();
    }
    statistics.setCurrentUsage(used);

    //current total pod count
    int total = (int) pods.values().stream()
        .filter(pod -> pod.getState().isAlive())
        .count();
    statistics.setCurrentTotalPodCount(total);

    //current busy pod count
    int busy = (int) pods.values().stream()
        .filter(pod -> pod.getState().isInUse() && pod.getUtilization() > 0)
        .count();
    statistics.setCurrentBusyPodCount(busy);
    statistics.setCurrentOnDemandPodCount(total - unmanagedPods - statistics.getCurrentSpotPodCount());

    //current guaranteed pod count
    statistics.setCurrentGuaranteedPodCount(report.getGuaranteedPodCount());
    return statistics;
  }

  protected void updateMetrics(InstanceStatistics statistics) {
    InventoryMetrics.setInstanceMaxSlots(name, statistics.getMaxPodCount());
    InventoryMetrics.setInstanceUsedSlots(name, statistics.getCurrentUsage());
    InventoryMetrics.setInstanceBusySlots(name, statistics.getCurrentBusyPodCount());
    InventoryMetrics.setInstanceTotalSlots(name, statistics.getCurrentTotalPodCount());
    InventoryMetrics.setInstanceCurrentSlots(name, "Reserved", statistics.getCurrentReservedPodCount());
    InventoryMetrics.setInstanceCurrentSlots(name, "OnDemand", statistics.getCurrentOnDemandPodCount());
    InventoryMetrics.setInstanceGuaranteedSlots(name, statistics.getCurrentGuaranteedPodCount());
    InventoryMetrics.setInstanceCurrentSlots(name, "Spot", statistics.getCurrentSpotPodCount());
  }

  protected void handlePodStatus(Map<String, PodStatus> pods) {
    this.writeLock.lock();
    try {
      InstanceState state = getState();
      if (state != InstanceState.SCALING_IN) {
        return;
      }
      Set<String> cancelDrainingNodes = new HashSet<>();
      for (PodStatus pod : pods.values()) {
        if (pod.getState() == PodState.DRAINED) {
          String nodeName = drainingPods.remove(pod.getPodName());
          if (nodeName == null) {
            LOG.debug("Pod {} not found in draining pods", pod.getPodName());
            continue;
          }
          LOG.info("Pod {} is drained, add it to drained pods and remove it from draining node {}",
              pod.getPodName(), nodeName);
          ImNode node = drainingNodes.get(nodeName);
          if (node != null) {
            node.removePodLoad(pod.getPodName());
          } else {
            LOG.warn("Node {} not found in draining nodes, should not reach here.", nodeName);
          }
          podsToScaleIn.add(pod.getPodName());
        } else if (pod.getState() == PodState.DRAINING) {
          if (!drainingPods.containsKey(pod.getPodName())) {
            LOG.info("Pod {} is not draining, undrain it", pod.getPodName());
            podsToUndrain.add(pod.getPodName());
          }
        } else {
          //client can decide to stop draining a pod, so we need to check if the pod is still draining
          String nodeName = drainingPods.remove(pod.getPodName());
          if (podsToUndrain.remove(pod.getPodName())) {
            LOG.info("Pod {} state is {}, remove it from undrain set", pod.getPodName(), pod.getState());
          }
          if (nodeName != null) {
            LOG.info("Pod {} is not draining, cancel draining node {} on which the pod is running, pod state: {}",
                pod.getPodName(), nodeName, pod.getState());
            cancelDrainingNodes.add(nodeName);
          }
        }
        //TODO handle node shutdown when draining.
        for (String nodeName : cancelDrainingNodes) {
          LOG.info("Node {} is not drained in instance {}, cancel draining pods on it", nodeName, name);
          Iterator<Map.Entry<String, String>> iterator = drainingPods.entrySet().iterator();
          while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            if (entry.getValue().equals(nodeName)) {
              LOG.info("Cancel draining pod {} in instance {}", entry.getKey(), name);
              podsToUndrain.add(entry.getKey());
              iterator.remove();
            }
          }
          for (PodStatus cancelPod : pods.values()) {
            if (cancelPod.getState() == PodState.DRAINED && nodeName.equals(podIdx.get(cancelPod.getPodName()))) {
              LOG.info("Cancel drained pod {} in instance {}, add it to undrain set",
                  cancelPod.getPodName(), nodeName);
              podsToUndrain.add(cancelPod.getPodName());
            }
          }
          drainingNodes.remove(nodeName);
        }
      }
    } finally {
      this.writeLock.unlock();
    }
  }

  protected void handleReport(InstanceReport report, InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode {
    LOG.debug("Handle report for instance {}, report: {}", name, report);
    latestReport.set(report);
    InstanceStatistics statistics = getStatistics(instanceConfig);
    updateMetrics(statistics);
    UsageLevelController.Action action = usageLevelController.syncAndJudge(statistics.getCurrentUsage(),
        statistics.getCurrentTotalPodCount(), report.getPendingRequests(), statistics.getMaxPodCount(),
        statistics.getCurrentGuaranteedPodCount());
    InstanceState state = getState();
    if (action != null && state == InstanceState.RUNNING) {
      if (action.isInOrOut() && instanceConfig.isAllowedAction(InventoryConfiguration.SCALE_IN_ACTION)) {
        LOG.info("Auto scale in instance {} to {}", name, action.getTargetSize());
        scaleIn((int) action.getTargetSize(), instanceConfig);
      } else if (!action.isInOrOut() && instanceConfig.isAllowedAction(InventoryConfiguration.SCALE_OUT_ACTION)) {
        LOG.info("Auto scale out instance {} to {}", name, action.getTargetSize());
        scaleOut((int) action.getTargetSize(), false, instanceConfig);
      }
      return;
    }
    if (report.getState() == state && report.getPodMap() != null && !report.getPodMap().isEmpty()) {
      handlePodStatus(report.getPodMap());
    }
  }

  protected boolean isDrained() {
    this.readLock.lock();
    try {
      if (instanceConfig().partialDrain()) {
        return drainingPods.isEmpty();
      }
      if (drainingNodes.isEmpty()) {
        return true;
      }
      for (ImNode node : drainingNodes.values()) {
        if (!node.isDrained()) {
          return false;
        }
      }
      return true;
    } finally {
      this.readLock.unlock();
    }
  }

  protected void finishDrain() {
    LOG.info("Finish drain for instance {}, draining nodes: {}, draining pods: {}", name, drainingNodes, drainingPods);
    this.writeLock.lock();
    try {
      if (!instanceConfig().partialDrain()) {
        nodesToScaleIn.clear();
        if (drainingNodes.isEmpty()) {
          return;
        }
        Iterator<Map.Entry<String, ImNode>> iterator = drainingNodes.entrySet().iterator();
        while (iterator.hasNext()) {
          Map.Entry<String, ImNode> entry = iterator.next();
          ImNode node = entry.getValue();
          if (node.isDrained()) {
            nodesToScaleIn.put(node.getNodeName(), node.getNodeInfo());
          }
          iterator.remove();
        }
      } else {
        if (drainingPods.isEmpty()) {
          return;
        }
      }
      cancelDrainingInternal();
    } finally {
      this.writeLock.unlock();
    }
  }

  protected abstract void scaleInPodsInternal(Set<String> pods,
                                              InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode;

  protected void scaleInDrainedPods(InventoryConfiguration.FrameworkInstanceConfig instanceConfig)
      throws ExceptionWithErrorCode {
    Set<String> pods;
    if (!instanceConfig.partialDrain()) {
      pods =
          podIdx.entrySet().stream().filter(idx -> nodesToScaleIn.containsKey(idx.getValue()))
              .map(Map.Entry::getKey).collect(Collectors.toSet());
    } else {
      pods = new HashSet<>(podsToScaleIn);
    }
    scaleInPodsInternal(pods, instanceConfig);
  }

  protected boolean checkPodsScaledIn() {
    InstanceReport report = latestReport.get();
    if (report == null) {
      LOG.warn("No report for instance {}", name);
      return false;
    }
    if (!report.isScaledIn()) {
      LOG.info("Instance {} is not scaled in, report: {}", name, report);
    }
    return report.isScaledIn();
  }

  protected boolean tryReimbursePool(int totalSize, Map<YarnProtos.NodeType, Integer> poolTargetSize, InventoryConfiguration.FrameworkInstanceConfig instanceConfig) throws RetryableException {
    boolean hasSpot = false;
    Map<YarnProtos.NodeType, Integer> activeSize = getElasticActivePodSize();
    Set<YarnProtos.NodeType> suppletivePools  = new HashSet<>();
    for (Map.Entry<YarnProtos.NodeType, InventoryConfiguration.NodeTypeConfig> conf : instanceConfig.getNodeType().entrySet()) {
      if (conf.getKey().equals(YarnProtos.NodeType.SPOT)) {
        hasSpot = true;
      } else {
        int poolTarget = poolTargetSize.getOrDefault(conf.getKey(), 0);
        int poolActive = activeSize.getOrDefault(conf.getKey(), 0);
        if (poolActive >= poolTarget) {
          suppletivePools.add(conf.getKey());
        } else {
          LOG.info("Instance {} on_demand pool {} target size {}, active size {}, NOT suppletive",
              name, conf.getKey(), poolTarget, poolActive);
        }
      }
    }

    if (hasSpot && !suppletivePools.isEmpty()) {
      Map<YarnProtos.NodeType, Float> evolvedRatio = new HashMap<>();
      float shortageRatio = 0F;
      for (Map.Entry<YarnProtos.NodeType, InventoryConfiguration.NodeTypeConfig> conf :
          instanceConfig.getNodeType().entrySet()) {
        if (poolTargetSize.containsKey(conf.getKey())) {
          int poolTarget = poolTargetSize.getOrDefault(conf.getKey(), 0);
          int poolActive = activeSize.getOrDefault(conf.getKey(), 0);
          if (poolActive < poolTarget && conf.getKey().equals(YarnProtos.NodeType.SPOT)) {
            float reimburseRatio = 1.0F * poolActive / totalSize;
            shortageRatio += conf.getValue().getRatio() - reimburseRatio;
            evolvedRatio.put(conf.getKey(), reimburseRatio);
            LOG.info("Instance {} deployment {} target size {}, active size {}, pool ratio reimburse to {}",
                name, conf.getValue().getKubeResName(), poolTarget, poolActive, reimburseRatio);
          }
        }
      }
      if (shortageRatio > 0) {
        float reimburseRatio = shortageRatio / suppletivePools.size();
        float reimburseFailRatio = 0;
        for (Map.Entry<YarnProtos.NodeType, InventoryConfiguration.NodeTypeConfig> conf :
            instanceConfig.getNodeType().entrySet()) {
          if (suppletivePools.contains(conf.getKey())) {
            float idealRatio = conf.getValue().getRatio() + reimburseRatio;
            int maxSize = conf.getValue().getMaxPodCount();
            float maxRatio = 1.0F * maxSize / totalSize;
            float actualRatio = Math.min(idealRatio, maxRatio);
            reimburseFailRatio += idealRatio - actualRatio;
            LOG.info("Instance {} type {} ratio reimburse from {} to {}, ideal ratio {}, max ratio {}",
                name, conf.getKey(), conf.getValue().getRatio(), actualRatio, idealRatio,
                maxRatio);
            evolvedRatio.put(conf.getKey(), actualRatio);
          }
        }

        if (reimburseFailRatio > 0) {
          for (Map.Entry<YarnProtos.NodeType, InventoryConfiguration.NodeTypeConfig> conf :
              instanceConfig.getNodeType().entrySet()) {
            if (conf.getKey().equals(YarnProtos.NodeType.SPOT)) {
              LOG.info("Instance {} type {} reimburse fail, restore ratio from {} to {}", name,
                  conf.getKey(), conf.getValue().getRatio(), conf.getValue().getRatio() + reimburseFailRatio);
              evolvedRatio.put(conf.getKey(),
                  evolvedRatio.getOrDefault(conf.getKey(), conf.getValue().getRatio()) + reimburseFailRatio);
            }
          }
        }

        Map<YarnProtos.NodeType, Integer> evolvedTargetSize = new HashMap<>();
        for (Map.Entry<YarnProtos.NodeType, Float> ratio : evolvedRatio.entrySet()) {
          evolvedTargetSize.put(ratio.getKey(), Math.round(ratio.getValue() * totalSize));
        }
        try {
          checkAndModifyTargetSize(evolvedTargetSize, true, instanceConfig);
          checkAndModifyTargetSize(evolvedTargetSize, false, instanceConfig);
        } catch (RetryableException e) {
          throw e;
        } catch (ExceptionWithErrorCode e) {
          throw new RuntimeException(e);
        }
        return true;
      }
    }
    return false;
  }

  protected void cancelDrainingInternal() {
    LOG.info("Cancel draining for instance {}, draining pods: {}, draining nodes: {}",
        name, drainingPods, drainingNodes);
    podsToUndrain.addAll(drainingPods.keySet());
    drainingPods.clear();
    if (!drainingNodes.isEmpty()) {
      InstanceReport report = latestReport.get();
      if (report != null) {
        report.getPodMap().values().forEach(pod -> {
          if (pod.getState() == PodState.DRAINED && drainingNodes.containsKey(podIdx.get(pod.getPodName()))) {
            podsToUndrain.add(pod.getPodName());
          }
        });
      }
      drainingNodes.clear();
    }
  }

  protected void cancelDraining() {
    this.writeLock.lock();
    try {
      cancelDrainingInternal();
    } finally {
      this.writeLock.unlock();
    }
  }

  public InstanceAction report(InstanceReport report) throws ExceptionWithErrorCode {
    InventoryConfiguration.FrameworkInstanceConfig instanceConfig = volatileConfig;
    handleReport(report, instanceConfig);
    InstanceAction instanceAction = new InstanceAction(frameworkType, instanceConfig.getVersion());
    this.readLock.lock();
    try {
      InstanceState state = getState();
      instanceAction.setState(state);
      if (state == InstanceState.SCALING_IN) {
        drainingPods.keySet().forEach(instanceAction::addPodToDrain);
        podsToUndrain.forEach(instanceAction::addPodToUndrain);
        podsToUndrain.clear();
      }
    } finally {
      this.readLock.unlock();
    }
    return instanceAction;
  }

  public void waitActionFinish() throws InterruptedException {
    try {
      actionDriver.waitForFinish(0);
    } catch (TimeoutException e) {
      throw new RuntimeException(e);
    } catch (InvalidParameterException e) {
      throw new RuntimeException(e);
    }
  }

  public InventoryManagerAdminServiceProtos.InstanceStatus getStatus() {
    this.readLock.lock();
    try {
      InventoryConfiguration.FrameworkInstanceConfig instanceConfig = volatileConfig;
      InventoryManagerAdminServiceProtos.InstanceStatus.Builder builder =
          InventoryManagerAdminServiceProtos.InstanceStatus.newBuilder();
      builder.setFramework(frameworkType.toString());
      builder.setVersion(instanceConfig.getVersion());
      builder.getUsageLevelStatusBuilder().mergeFrom(usageLevelController.getStatus());
      builder.setPreviousState(stateMachine.getPreviousState().toString());
      builder.setCurrentState(stateMachine.getCurrentState().toString());
      if (latestReport.get() != null) {
        builder.setLastHeartbeatTs(latestReport.get().getTimestamp());
        builder.setAliveReplicas((int) latestReport.get().getPodMap().values().stream()
            .filter(pod -> pod.getState().isAlive()).count());
      } else {
        builder.setLastHeartbeatTs(0);
      }
      builder.setLastStateTransitionTs(lastStateTransitionTime);
      return builder.build();
    } finally {
      this.readLock.unlock();
    }
  }

  public synchronized void setInstanceConfig(InventoryConfiguration.FrameworkInstanceConfig instanceConfig) {
    if (deleting.compareAndSet(true, false)) {
      LOG.info("Cancel deleting instance {}", name);
    }
    if (volatileConfig.equals(instanceConfig)) {
      return;
    }
    if (!volatileConfig.getVersion().equals(instanceConfig.getVersion())) {
      LOG.error("Cannot update version for instance {} from {} to {}, should not reach here", name,
          volatileConfig.getVersion(), instanceConfig.getVersion());
      throw new RuntimeException("Cannot update version for instance " + name);
    }
    LOG.info("Set instance config for instance {} from {} to {}", name,
        volatileConfig.toJson(), instanceConfig.toJson());
    UsageLevelController.ULCConfig ulcConfig = new UsageLevelController.ULCConfig(
        instanceConfig.getMinUsedThreshold(),
        instanceConfig.getMaxUsedThreshold(),
        instanceConfig.getTargetUsedThreshold(),
        instanceConfig.getScaleOutDelayMs(),
        instanceConfig.getScaleInDelayMs(),
        instanceConfig.getPendingDelayMs());
    usageLevelController.setConfig(ulcConfig);
    this.volatileConfig = instanceConfig;
  }

  public void beginDrop() {
    LOG.info("Begin dropping instance {}, previous deleting: {}", name, deleting.get());
    deleting.set(true);
  }

  protected void tryDelete() throws ExceptionWithErrorCode {
    LOG.info("Try to delete instance {}", name);
    InstanceEvent event = new InstanceEvent(InstanceEventType.PREPARE_DROP);
    handleEventInterval(event);
  }

  protected InstanceState prepareDrop() throws ExceptionWithErrorCode {
    LOG.info("Prepare drop instance {}", name);
    return dropInternal();
  }

  protected InstanceState dropInternal() throws ExceptionWithErrorCode {
    LOG.info("Dropping instance {}", name);
    InventoryConfiguration.FrameworkInstanceConfig instanceConfig = volatileConfig;
    Map<YarnProtos.NodeType, Integer> desiredSize = getDesiredPodSize(instanceConfig);
    Map<YarnProtos.NodeType, Integer> readySize = getReadyPodSize(instanceConfig);
    if (!desiredSize.equals(readySize)) {
      LOG.info("Instance {} is not ready to drop, desired size: {}, ready size: {}, cancel drop",
          name, desiredSize, readySize);
      return InstanceState.RUNNING;
    }
    context.getInventoryManager().getFrameworkMap().computeIfPresent(frameworkType, (k, v) -> {
      if (v.getInstances().remove(instanceConfig.getVersion()) != null) {
        LOG.info("Removed instance {} from framework {}", name, frameworkType);
        if (v.getInstances().isEmpty()) {
          LOG.info("Removed framework {} from inventory manager", frameworkType);
          return null;
        }
      }
      return v;
    });
    InventoryMetrics.removeInstance(name);
    return InstanceState.DROPPED;
  }

  protected InstanceState prepareBalance() throws ExceptionWithErrorCode {
    LOG.info("Prepare balance instance {}", name);
    InstanceState state = balance();
    if (state != InstanceState.RUNNING) {
      unBalanceTime = 0;
    }
    return state;
  }

  protected InstanceState balance() throws ExceptionWithErrorCode {
    InventoryConfiguration.FrameworkInstanceConfig instanceConfig = volatileConfig;
    Map<YarnProtos.NodeType, Integer> currentSize = getDesiredPodSize(instanceConfig);
    int totalSize = currentSize.values().stream().mapToInt(Integer::intValue).sum();
    Map<YarnProtos.NodeType, Integer> balancedSize = InventoryUtils.getSizeByPool(totalSize,
        getTypeAndRatio(instanceConfig));

    InstanceStatistics statistics = getStatistics(instanceConfig);
    if (statistics.getCurrentTotalPodCount() == 0) {
      LOG.info("Ignore balancing instance {} since total pod count 0", name);
      return InstanceState.RUNNING;
    }
    float usage = (float) (statistics.getCurrentUsage() / statistics.getCurrentTotalPodCount());
    if (usage < instanceConfig.getMinUsedThreshold() || usage > instanceConfig.getMaxUsedThreshold()) {
      LOG.info("Ignore balancing instance {} since usage level abnormal {}", name, usage);
      unBalanceTime = 0;
      return InstanceState.RUNNING;
    }

    if (usage < instanceConfig.getTargetUsedThreshold()) {
      LOG.info("Instance {} usage {} smaller than target {}, prepare to scale in", name, usage,
          instanceConfig.getTargetUsedThreshold());
      YarnProtos.NodeType baseType = null;
      for (Map.Entry<YarnProtos.NodeType, InventoryConfiguration.NodeTypeConfig> conf :
          instanceConfig.getNodeType().entrySet()) {
        if (currentSize.get(conf.getKey()) < balancedSize.get(conf.getKey())) {
          baseType = conf.getKey();
        }
      }
      if (baseType != null) {
        int maxScaleCount =
            (int) (statistics.getCurrentTotalPodCount() - Math.ceil(statistics.getCurrentUsage() / instanceConfig.getMaxUsedThreshold()));
        if (maxScaleCount <= 0) {
          LOG.info("Instance {} current total size {} exceeds max threshold count {}", name, totalSize,
              Math.ceil(statistics.getCurrentUsage() / instanceConfig.getMaxUsedThreshold()));
          return InstanceState.RUNNING;
        }
        Map<YarnProtos.NodeType, Integer> podsToDelete = new HashMap<>();
        for (Map.Entry<YarnProtos.NodeType, InventoryConfiguration.NodeTypeConfig> conf :
            instanceConfig.getNodeType().entrySet()) {
          int current = currentSize.get(conf.getKey());
          if (current > balancedSize.get(conf.getKey())) {
            int idealSize = 0;
            if (instanceConfig.getNodeType().get(baseType).getRatio() > 0) {
              idealSize =
                  Math.round(currentSize.get(baseType) * conf.getValue().getRatio() / instanceConfig.getNodeType().get(baseType).getRatio());
            } else {
              idealSize = current - maxScaleCount;
            }
            if (current - idealSize <= maxScaleCount) {
              maxScaleCount -= (current - idealSize);
              podsToDelete.put(conf.getKey(), current - idealSize);
            } else {
              podsToDelete.put(conf.getKey(), maxScaleCount);
              idealSize = current - maxScaleCount;
              maxScaleCount = 0;
            }
            LOG.info("Instance {} pool {} scale in from {} to {} for balancing", name, conf.getKey(), current,
                idealSize);
          }
        }
        if (!podsToDelete.isEmpty()) {
          LOG.info("Balancing instance {}", name);
          judgeCandidatePods2Drain(podsToDelete, instanceConfig);
          return checkAndSubmitScaleInAction(instanceConfig);
        }
      } else {
        LOG.info("Instance {} not found base pool", name);
      }
    } else {
      // scale out
      LOG.info("Instance {} usage {} lager than target {}, prepare to scale out", name, usage,
          instanceConfig.getTargetUsedThreshold());
      YarnProtos.NodeType basePool = null;
      for (Map.Entry<YarnProtos.NodeType, InventoryConfiguration.NodeTypeConfig> conf :
          instanceConfig.getNodeType().entrySet()) {
        if (currentSize.get(conf.getKey()) > balancedSize.get(conf.getKey())) {
          basePool = conf.getKey();
          break;
        }
      }
      Map<YarnProtos.NodeType, Integer> targetSize = new HashMap<>();
      if (basePool != null) {
        int maxScaleCount =
            (int) (Math.ceil(statistics.getCurrentUsage() / instanceConfig.getMinUsedThreshold()) - statistics.getCurrentTotalPodCount());
        for (Map.Entry<YarnProtos.NodeType, InventoryConfiguration.NodeTypeConfig> conf :
            instanceConfig.getNodeType().entrySet()) {
          int current = currentSize.get(conf.getKey());
          if (current < balancedSize.get(conf.getKey())) {
            int idealSize = 0;
            if (instanceConfig.getNodeType().get(basePool).getRatio() > 0) {
              idealSize =
                  Math.round(currentSize.get(basePool) * conf.getValue().getRatio() / instanceConfig.getNodeType().get(basePool).getRatio());
            } else {
              idealSize = current + maxScaleCount;
            }
            if (idealSize - current <= maxScaleCount) {
              maxScaleCount -= (idealSize - current);
            } else {
              idealSize = current + maxScaleCount;
              maxScaleCount = 0;
            }
            targetSize.put(conf.getKey(), idealSize);
            totalSize += idealSize - currentSize.getOrDefault(conf.getKey(), 0);
            LOG.info("Instance {} pool {} scale out from {} to {} for balancing", name, conf.getKey(), current,
                idealSize);
          }
        }
        if (!targetSize.isEmpty()) {
          LOG.info("Balancing instance {}", name);
          Map<YarnProtos.NodeType, Integer> normalizedSize = normalizeTargetSize(targetSize, instanceConfig);
          actionDriver.submit(ScaleOutInstanceAction.create(this, statistics.getCurrentTotalPodCount(),
              totalSize, normalizedSize, false, instanceConfig));
          return InstanceState.SCALING_OUT;
        }
      } else {
        LOG.info("Instance {} not found base pool", name);
      }
    }
    return InstanceState.RUNNING;
  }

  public static class PrepareScaleOutTransition implements
      MultipleArcTransition<AbstractInstance, InstanceEvent, InstanceState> {
    @Override
    public InstanceState transition(AbstractInstance instance, InstanceEvent event) {
      try {
        PrepareScaleOutInstanceEvent prepareScaleOutInstanceEvent = (PrepareScaleOutInstanceEvent) event;
        return instance.prepareScaleOut(prepareScaleOutInstanceEvent.getTargetSize(),
            prepareScaleOutInstanceEvent.isAllOrNothing(),
            prepareScaleOutInstanceEvent.getInstanceConfig());
      } catch (ExceptionWithErrorCode e) {
        throw new RuntimeException(e);
      }
    }
  }

  public static class PrepareScaleInTransition implements
      MultipleArcTransition<AbstractInstance, InstanceEvent, InstanceState> {
    @Override
    public InstanceState transition(AbstractInstance instance, InstanceEvent event) {
      try {
        PrepareScaleInInstanceEvent prepareScaleInInstanceEvent = (PrepareScaleInInstanceEvent) event;
        return instance.prepareScaleIn(prepareScaleInInstanceEvent.getTargetSize(),
            prepareScaleInInstanceEvent.getInstanceConfig());
      } catch (ExceptionWithErrorCode e) {
        throw new RuntimeException(e);
      }
    }
  }

  public static class ScaleInInstanceErrorTransition implements
      SingleArcTransition<AbstractInstance, InstanceEvent> {
    @Override
    public void transition(AbstractInstance instance, InstanceEvent event) {
      LOG.warn("Scale in instance {} error, cancel draining", instance.getName());
      instance.cancelDraining();
    }
  }

  public static class PrepareDropTransition implements
      MultipleArcTransition<AbstractInstance, InstanceEvent, InstanceState> {
    @Override
    public InstanceState transition(AbstractInstance instance, InstanceEvent event) {
      try {
        return instance.prepareDrop();
      } catch (ExceptionWithErrorCode e) {
        throw new RuntimeException(e);
      }
    }
  }

  public static class PrepareBalanceTransition implements
      MultipleArcTransition<AbstractInstance, InstanceEvent, InstanceState> {
    @Override
    public InstanceState transition(AbstractInstance instance, InstanceEvent event) {
      try {
        return instance.prepareBalance();
      } catch (ExceptionWithErrorCode e) {
        throw new RuntimeException(e);
      }
    }
  }
}
