package com.clickzetta.rm.job

import com.nimbusds.jose._
import com.nimbusds.jose.crypto.{<PERSON>Signer, MACVerifier}

object Utils {

  private val idGenerator = CZRequestIdGenerator.getInstance()

  def generateJobId(): String = idGenerator.generate()

  def generateRequestId(): String = idGenerator.generate()

  /**
   * Convert a time parameter such as (50s, 100ms, or 250us) to milliseconds for internal use. If
   * no suffix is provided, the passed number is assumed to be in ms.
   */
  def timeStringAsMs(str: String): Long = {
    JavaUtils.timeStringAsMs(str)
  }

  /**
   * Convert a time parameter such as (50s, 100ms, or 250us) to seconds for internal use. If
   * no suffix is provided, the passed number is assumed to be in seconds.
   */
  def timeStringAsSeconds(str: String): Long = {
    JavaUtils.timeStringAsSec(str)
  }

  /**
   * Convert a passed byte string (e.g. 50b, 100k, or 250m) to bytes for internal use.
   *
   * If no suffix is provided, the passed number is assumed to be in bytes.
   */
  def byteStringAsBytes(str: String): Long = {
    JavaUtils.byteStringAsBytes(str)
  }

  /**
   * Convert a passed byte string (e.g. 50b, 100k, or 250m) to kibibytes for internal use.
   *
   * If no suffix is provided, the passed number is assumed to be in kibibytes.
   */
  def byteStringAsKb(str: String): Long = {
    JavaUtils.byteStringAsKb(str)
  }

  /**
   * Convert a passed byte string (e.g. 50b, 100k, or 250m) to mebibytes for internal use.
   *
   * If no suffix is provided, the passed number is assumed to be in mebibytes.
   */
  def byteStringAsMb(str: String): Long = {
    JavaUtils.byteStringAsMb(str)
  }

  /**
   * Convert a passed byte string (e.g. 50b, 100k, or 250m, 500g) to gibibytes for internal use.
   *
   * If no suffix is provided, the passed number is assumed to be in gibibytes.
   */
  def byteStringAsGb(str: String): Long = {
    JavaUtils.byteStringAsGb(str)
  }

  /**
   * Convert a Java memory parameter passed to -Xmx (such as 300m or 1g) to a number of mebibytes.
   */
  def memoryStringToMb(str: String): Int = {
    // Convert to bytes, rather than directly to MiB, because when no units are specified the unit
    // is assumed to be bytes
    (JavaUtils.byteStringAsBytes(str) / 1024 / 1024).toInt
  }

  def generateToken(payloadStr: String, secret: String): String = {
    val header = new JWSHeader.Builder(JWSAlgorithm.HS256)
        .`type`(JOSEObjectType.JWT)
        .build()
    val payload = new Payload(payloadStr)
    val jwsObject = new JWSObject(header, payload)
    val signer = new MACSigner(secret)
    jwsObject.sign(signer)
    jwsObject.serialize
  }

  def verifyToken(token: String, secret: String): String = {
    val jwsObject = JWSObject.parse(token)
    val verifier = new MACVerifier(secret)
    if (!jwsObject.verify(verifier)) {
      throw new IllegalArgumentException("invalid JWT token!")
    }
    jwsObject.getPayload.toString
  }
}
