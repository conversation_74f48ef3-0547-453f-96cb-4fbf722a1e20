<?xml version="1.0" encoding="iso-8859-1" standalone="no"?>
<!-- Generated by the JDiff Javadoc doclet -->
<!-- (http://www.jdiff.org) -->
<!-- on Wed Aug 24 13:55:20 PDT 2016 -->

<api
  xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
  xsi:noNamespaceSchemaLocation='api.xsd'
  name="hadoop-yarn-server-common 2.7.2"
  jdversion="1.0.9">

<!--  Command line arguments =  -doclet org.apache.hadoop.classification.tools.IncludePublicAnnotationsJDiffDoclet -docletpath /Users/<USER>/Workspace/eclipse-workspace/apache-git/hadoop/hadoop-yarn-project/hadoop-yarn/hadoop-yarn-server/hadoop-yarn-server-common/target/hadoop-annotations.jar:/Users/<USER>/Workspace/eclipse-workspace/apache-git/hadoop/hadoop-yarn-project/hadoop-yarn/hadoop-yarn-server/hadoop-yarn-server-common/target/jdiff.jar -verbose -classpath /Users/<USER>/Workspace/eclipse-workspace/apache-git/hadoop/hadoop-yarn-project/hadoop-yarn/hadoop-yarn-server/hadoop-yarn-server-common/target/classes:/Users/<USER>/Workspace/eclipse-workspace/apache-git/hadoop/hadoop-common-project/hadoop-common/target/hadoop-common-2.7.2.jar:/Users/<USER>/.m2/repository/commons-cli/commons-cli/1.2/commons-cli-1.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.1.1/commons-math3-3.1.1.jar:/Users/<USER>/.m2/repository/xmlenc/xmlenc/0.52/xmlenc-0.52.jar:/Users/<USER>/.m2/repository/commons-httpclient/commons-httpclient/3.1/commons-httpclient-3.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.4/commons-codec-1.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar:/Users/<USER>/.m2/repository/commons-net/commons-net/3.1/commons-net-3.1.jar:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/Users/<USER>/.m2/repository/javax/servlet/servlet-api/2.5/servlet-api-2.5.jar:/Users/<USER>/.m2/repository/org/mortbay/jetty/jetty/6.1.26/jetty-6.1.26.jar:/Users/<USER>/.m2/repository/org/mortbay/jetty/jetty-util/6.1.26/jetty-util-6.1.26.jar:/Users/<USER>/.m2/repository/javax/servlet/jsp/jsp-api/2.1/jsp-api-2.1.jar:/Users/<USER>/.m2/repository/com/sun/jersey/jersey-core/1.9/jersey-core-1.9.jar:/Users/<USER>/.m2/repository/com/sun/jersey/jersey-json/1.9/jersey-json-1.9.jar:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar:/Users/<USER>/.m2/repository/com/sun/xml/bind/jaxb-impl/2.2.3-1/jaxb-impl-2.2.3-1.jar:/Users/<USER>/.m2/repository/com/sun/jersey/jersey-server/1.9/jersey-server-1.9.jar:/Users/<USER>/.m2/repository/asm/asm/3.2/asm-3.2.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.17/log4j-1.2.17.jar:/Users/<USER>/.m2/repository/net/java/dev/jets3t/jets3t/0.9.0/jets3t-0.9.0.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.2.5/httpclient-4.2.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.2.5/httpcore-4.2.5.jar:/Users/<USER>/.m2/repository/com/jamesmurty/utils/java-xmlbuilder/0.4/java-xmlbuilder-0.4.jar:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.6/commons-configuration-1.6.jar:/Users/<USER>/.m2/repository/commons-digester/commons-digester/1.8/commons-digester-1.8.jar:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.7.0/commons-beanutils-1.7.0.jar:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils-core/1.8.0/commons-beanutils-core-1.8.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.10/slf4j-api-1.7.10.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-log4j12/1.7.10/slf4j-log4j12-1.7.10.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.9.13/jackson-core-asl-1.9.13.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.9.13/jackson-mapper-asl-1.9.13.jar:/Users/<USER>/.m2/repository/org/apache/avro/avro/1.7.4/avro-1.7.4.jar:/Users/<USER>/.m2/repository/com/thoughtworks/paranamer/paranamer/2.3/paranamer-2.3.jar:/Users/<USER>/.m2/repository/org/xerial/snappy/snappy-java/*******/snappy-java-*******.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.2.4/gson-2.2.4.jar:/Users/<USER>/Workspace/eclipse-workspace/apache-git/hadoop/hadoop-common-project/hadoop-auth/target/hadoop-auth-2.7.2.jar:/Users/<USER>/.m2/repository/org/apache/directory/server/apacheds-kerberos-codec/2.0.0-M15/apacheds-kerberos-codec-2.0.0-M15.jar:/Users/<USER>/.m2/repository/org/apache/directory/server/apacheds-i18n/2.0.0-M15/apacheds-i18n-2.0.0-M15.jar:/Users/<USER>/.m2/repository/org/apache/directory/api/api-asn1-api/1.0.0-M20/api-asn1-api-1.0.0-M20.jar:/Users/<USER>/.m2/repository/org/apache/directory/api/api-util/1.0.0-M20/api-util-1.0.0-M20.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/2.7.1/curator-framework-2.7.1.jar:/Users/<USER>/.m2/repository/com/jcraft/jsch/0.1.42/jsch-0.1.42.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/2.7.1/curator-client-2.7.1.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/2.7.1/curator-recipes-2.7.1.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.0/jsr305-3.0.0.jar:/Users/<USER>/.m2/repository/org/apache/htrace/htrace-core/3.1.0-incubating/htrace-core-3.1.0-incubating.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.4.1/commons-compress-1.4.1.jar:/Users/<USER>/.m2/repository/org/tukaani/xz/1.0/xz-1.0.jar:/Users/<USER>/Workspace/eclipse-workspace/apache-git/hadoop/hadoop-yarn-project/hadoop-yarn/hadoop-yarn-api/target/hadoop-yarn-api-2.7.2.jar:/Users/<USER>/Workspace/eclipse-workspace/apache-git/hadoop/hadoop-yarn-project/hadoop-yarn/hadoop-yarn-common/target/hadoop-yarn-common-2.7.2.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.2.2/jaxb-api-2.2.2.jar:/Users/<USER>/.m2/repository/javax/xml/stream/stax-api/1.0-2/stax-api-1.0-2.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/.m2/repository/com/sun/jersey/jersey-client/1.9/jersey-client-1.9.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.9.13/jackson-jaxrs-1.9.13.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-xc/1.9.13/jackson-xc-1.9.13.jar:/Users/<USER>/.m2/repository/com/google/inject/extensions/guice-servlet/3.0/guice-servlet-3.0.jar:/Users/<USER>/.m2/repository/com/google/inject/guice/3.0/guice-3.0.jar:/Users/<USER>/.m2/repository/javax/inject/javax.inject/1/javax.inject-1.jar:/Users/<USER>/.m2/repository/aopalliance/aopalliance/1.0/aopalliance-1.0.jar:/Users/<USER>/.m2/repository/com/sun/jersey/contribs/jersey-guice/1.9/jersey-guice-1.9.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/11.0.2/guava-11.0.2.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.3/commons-logging-1.1.3.jar:/Users/<USER>/Workspace/eclipse-workspace/apache-git/hadoop/hadoop-common-project/hadoop-annotations/target/hadoop-annotations-2.7.2.jar:/Library/Java/JavaVirtualMachines/jdk1.8.0_40.jdk/Contents/Home/lib/tools.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/2.5.0/protobuf-java-2.5.0.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.6.2.Final/netty-3.6.2.Final.jar:/Users/<USER>/.m2/repository/org/fusesource/leveldbjni/leveldbjni-all/1.8/leveldbjni-all-1.8.jar -sourcepath /Users/<USER>/Workspace/eclipse-workspace/apache-git/hadoop/hadoop-yarn-project/hadoop-yarn/hadoop-yarn-server/hadoop-yarn-server-common/src/main/java -doclet org.apache.hadoop.classification.tools.IncludePublicAnnotationsJDiffDoclet -docletpath /Users/<USER>/Workspace/eclipse-workspace/apache-git/hadoop/hadoop-yarn-project/hadoop-yarn/hadoop-yarn-server/hadoop-yarn-server-common/target/hadoop-annotations.jar:/Users/<USER>/Workspace/eclipse-workspace/apache-git/hadoop/hadoop-yarn-project/hadoop-yarn/hadoop-yarn-server/hadoop-yarn-server-common/target/jdiff.jar -apidir /Users/<USER>/Workspace/eclipse-workspace/apache-git/hadoop/hadoop-yarn-project/hadoop-yarn/hadoop-yarn-server/hadoop-yarn-server-common/target/site/jdiff/xml -apiname hadoop-yarn-server-common 2.7.2 -->
<package name="org.apache.hadoop.yarn.server">
</package>
<package name="org.apache.hadoop.yarn.server.api">
</package>
<package name="org.apache.hadoop.yarn.server.api.impl.pb.client">
</package>
<package name="org.apache.hadoop.yarn.server.api.impl.pb.service">
</package>
<package name="org.apache.hadoop.yarn.server.api.records">
  <!-- start class org.apache.hadoop.yarn.server.api.records.NodeHealthStatus -->
  <class name="NodeHealthStatus" extends="java.lang.Object"
    abstract="true"
    static="false" final="false" visibility="public"
    deprecated="not deprecated">
    <constructor name="NodeHealthStatus"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </constructor>
    <method name="getIsNodeHealthy" return="boolean"
      abstract="true" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <doc>
      <![CDATA[Is the node healthy?
 @return <code>true</code> if the node is healthy, else <code>false</code>]]>
      </doc>
    </method>
    <method name="getHealthReport" return="java.lang.String"
      abstract="true" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <doc>
      <![CDATA[Get the <em>diagnostic health report</em> of the node.
 @return <em>diagnostic health report</em> of the node]]>
      </doc>
    </method>
    <method name="getLastHealthReportTime" return="long"
      abstract="true" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <doc>
      <![CDATA[Get the <em>last timestamp</em> at which the health report was received.
 @return <em>last timestamp</em> at which the health report was received]]>
      </doc>
    </method>
    <doc>
    <![CDATA[{@code NodeHealthStatus} is a summary of the health status of the node.
 <p>
 It includes information such as:
 <ul>
   <li>
     An indicator of whether the node is healthy, as determined by the
     health-check script.
   </li>
   <li>The previous time at which the health status was reported.</li>
   <li>A diagnostic report on the health status.</li>
 </ul>

 @see NodeReport
 @see ApplicationClientProtocol#getClusterNodes(org.apache.hadoop.yarn.api.protocolrecords.GetClusterNodesRequest)]]>
    </doc>
  </class>
  <!-- end class org.apache.hadoop.yarn.server.api.records.NodeHealthStatus -->
</package>
<package name="org.apache.hadoop.yarn.server.api.records.impl.pb">
</package>
<package name="org.apache.hadoop.yarn.server.metrics">
</package>
<package name="org.apache.hadoop.yarn.server.records">
</package>
<package name="org.apache.hadoop.yarn.server.records.impl.pb">
</package>
<package name="org.apache.hadoop.yarn.server.security.http">
</package>
<package name="org.apache.hadoop.yarn.server.sharedcache">
</package>
<package name="org.apache.hadoop.yarn.server.utils">
  <!-- start class org.apache.hadoop.yarn.server.utils.LeveldbIterator -->
  <class name="LeveldbIterator" extends="java.lang.Object"
    abstract="false"
    static="false" final="false" visibility="public"
    deprecated="not deprecated">
    <implements name="java.util.Iterator"/>
    <implements name="java.io.Closeable"/>
    <constructor name="LeveldbIterator" type="org.iq80.leveldb.DB"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <doc>
      <![CDATA[Create an iterator for the specified database]]>
      </doc>
    </constructor>
    <constructor name="LeveldbIterator" type="org.iq80.leveldb.DB, org.iq80.leveldb.ReadOptions"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <doc>
      <![CDATA[Create an iterator for the specified database]]>
      </doc>
    </constructor>
    <constructor name="LeveldbIterator" type="org.iq80.leveldb.DBIterator"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <doc>
      <![CDATA[Create an iterator using the specified underlying DBIterator]]>
      </doc>
    </constructor>
    <method name="seek"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <param name="key" type="byte[]"/>
      <exception name="DBException" type="org.iq80.leveldb.DBException"/>
      <doc>
      <![CDATA[Repositions the iterator so the key of the next BlockElement
 returned greater than or equal to the specified targetKey.]]>
      </doc>
    </method>
    <method name="seekToFirst"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <exception name="DBException" type="org.iq80.leveldb.DBException"/>
      <doc>
      <![CDATA[Repositions the iterator so is is at the beginning of the Database.]]>
      </doc>
    </method>
    <method name="seekToLast"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <exception name="DBException" type="org.iq80.leveldb.DBException"/>
      <doc>
      <![CDATA[Repositions the iterator so it is at the end of of the Database.]]>
      </doc>
    </method>
    <method name="hasNext" return="boolean"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <exception name="DBException" type="org.iq80.leveldb.DBException"/>
      <doc>
      <![CDATA[Returns <tt>true</tt> if the iteration has more elements.]]>
      </doc>
    </method>
    <method name="next" return="java.util.Map.Entry"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <exception name="DBException" type="org.iq80.leveldb.DBException"/>
      <doc>
      <![CDATA[Returns the next element in the iteration.]]>
      </doc>
    </method>
    <method name="peekNext" return="java.util.Map.Entry"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <exception name="DBException" type="org.iq80.leveldb.DBException"/>
      <doc>
      <![CDATA[Returns the next element in the iteration, without advancing the
 iteration.]]>
      </doc>
    </method>
    <method name="hasPrev" return="boolean"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <exception name="DBException" type="org.iq80.leveldb.DBException"/>
      <doc>
      <![CDATA[@return true if there is a previous entry in the iteration.]]>
      </doc>
    </method>
    <method name="prev" return="java.util.Map.Entry"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <exception name="DBException" type="org.iq80.leveldb.DBException"/>
      <doc>
      <![CDATA[@return the previous element in the iteration and rewinds the iteration.]]>
      </doc>
    </method>
    <method name="peekPrev" return="java.util.Map.Entry"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <exception name="DBException" type="org.iq80.leveldb.DBException"/>
      <doc>
      <![CDATA[@return the previous element in the iteration, without rewinding the
 iteration.]]>
      </doc>
    </method>
    <method name="remove"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <exception name="DBException" type="org.iq80.leveldb.DBException"/>
      <doc>
      <![CDATA[Removes from the database the last element returned by the iterator.]]>
      </doc>
    </method>
    <method name="close"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <exception name="IOException" type="java.io.IOException"/>
      <doc>
      <![CDATA[Closes the iterator.]]>
      </doc>
    </method>
    <doc>
    <![CDATA[A wrapper for a DBIterator to translate the raw RuntimeExceptions that
 can be thrown into DBExceptions.]]>
    </doc>
  </class>
  <!-- end class org.apache.hadoop.yarn.server.utils.LeveldbIterator -->
</package>
<package name="org.apache.hadoop.yarn.server.webapp">
</package>
<package name="org.apache.hadoop.yarn.server.webapp.dao">
  <!-- start class org.apache.hadoop.yarn.server.webapp.dao.AppAttemptInfo -->
  <class name="AppAttemptInfo" extends="java.lang.Object"
    abstract="false"
    static="false" final="false" visibility="public"
    deprecated="not deprecated">
    <constructor name="AppAttemptInfo"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </constructor>
    <constructor name="AppAttemptInfo" type="org.apache.hadoop.yarn.api.records.ApplicationAttemptReport"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </constructor>
    <method name="getAppAttemptId" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getHost" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getRpcPort" return="int"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getTrackingUrl" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getOriginalTrackingUrl" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getDiagnosticsInfo" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getAppAttemptState" return="org.apache.hadoop.yarn.api.records.YarnApplicationAttemptState"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getAmContainerId" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <field name="appAttemptId" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="host" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="rpcPort" type="int"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="trackingUrl" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="originalTrackingUrl" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="diagnosticsInfo" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="appAttemptState" type="org.apache.hadoop.yarn.api.records.YarnApplicationAttemptState"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="amContainerId" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
  </class>
  <!-- end class org.apache.hadoop.yarn.server.webapp.dao.AppAttemptInfo -->
  <!-- start class org.apache.hadoop.yarn.server.webapp.dao.AppAttemptsInfo -->
  <class name="AppAttemptsInfo" extends="java.lang.Object"
    abstract="false"
    static="false" final="false" visibility="public"
    deprecated="not deprecated">
    <constructor name="AppAttemptsInfo"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </constructor>
    <method name="add"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <param name="info" type="org.apache.hadoop.yarn.server.webapp.dao.AppAttemptInfo"/>
    </method>
    <method name="getAttempts" return="java.util.ArrayList"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <field name="attempt" type="java.util.ArrayList"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
  </class>
  <!-- end class org.apache.hadoop.yarn.server.webapp.dao.AppAttemptsInfo -->
  <!-- start class org.apache.hadoop.yarn.server.webapp.dao.AppInfo -->
  <class name="AppInfo" extends="java.lang.Object"
    abstract="false"
    static="false" final="false" visibility="public"
    deprecated="not deprecated">
    <constructor name="AppInfo"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </constructor>
    <constructor name="AppInfo" type="org.apache.hadoop.yarn.api.records.ApplicationReport"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </constructor>
    <method name="getAppId" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getCurrentAppAttemptId" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getUser" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getName" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getQueue" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getType" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getHost" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getRpcPort" return="int"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getAppState" return="org.apache.hadoop.yarn.api.records.YarnApplicationState"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getProgress" return="float"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getDiagnosticsInfo" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getOriginalTrackingUrl" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getTrackingUrl" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getFinalAppStatus" return="org.apache.hadoop.yarn.api.records.FinalApplicationStatus"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getSubmittedTime" return="long"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getStartedTime" return="long"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getFinishedTime" return="long"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getElapsedTime" return="long"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getApplicationTags" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <field name="appId" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="currentAppAttemptId" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="user" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="name" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="queue" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="type" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="host" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="rpcPort" type="int"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="appState" type="org.apache.hadoop.yarn.api.records.YarnApplicationState"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="progress" type="float"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="diagnosticsInfo" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="originalTrackingUrl" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="trackingUrl" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="finalAppStatus" type="org.apache.hadoop.yarn.api.records.FinalApplicationStatus"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="submittedTime" type="long"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="startedTime" type="long"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="finishedTime" type="long"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="elapsedTime" type="long"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="applicationTags" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
  </class>
  <!-- end class org.apache.hadoop.yarn.server.webapp.dao.AppInfo -->
  <!-- start class org.apache.hadoop.yarn.server.webapp.dao.AppsInfo -->
  <class name="AppsInfo" extends="java.lang.Object"
    abstract="false"
    static="false" final="false" visibility="public"
    deprecated="not deprecated">
    <constructor name="AppsInfo"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </constructor>
    <method name="add"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <param name="appinfo" type="org.apache.hadoop.yarn.server.webapp.dao.AppInfo"/>
    </method>
    <method name="getApps" return="java.util.ArrayList"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <field name="app" type="java.util.ArrayList"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
  </class>
  <!-- end class org.apache.hadoop.yarn.server.webapp.dao.AppsInfo -->
  <!-- start class org.apache.hadoop.yarn.server.webapp.dao.ContainerInfo -->
  <class name="ContainerInfo" extends="java.lang.Object"
    abstract="false"
    static="false" final="false" visibility="public"
    deprecated="not deprecated">
    <constructor name="ContainerInfo"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </constructor>
    <constructor name="ContainerInfo" type="org.apache.hadoop.yarn.api.records.ContainerReport"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </constructor>
    <method name="getContainerId" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getAllocatedMB" return="int"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getAllocatedVCores" return="int"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getAssignedNodeId" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getPriority" return="int"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getStartedTime" return="long"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getFinishedTime" return="long"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getElapsedTime" return="long"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getDiagnosticsInfo" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getLogUrl" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getContainerExitStatus" return="int"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getContainerState" return="org.apache.hadoop.yarn.api.records.ContainerState"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <method name="getNodeHttpAddress" return="java.lang.String"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <field name="containerId" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="allocatedMB" type="int"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="allocatedVCores" type="int"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="assignedNodeId" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="priority" type="int"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="startedTime" type="long"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="finishedTime" type="long"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="elapsedTime" type="long"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="diagnosticsInfo" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="logUrl" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="containerExitStatus" type="int"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="containerState" type="org.apache.hadoop.yarn.api.records.ContainerState"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
    <field name="nodeHttpAddress" type="java.lang.String"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
  </class>
  <!-- end class org.apache.hadoop.yarn.server.webapp.dao.ContainerInfo -->
  <!-- start class org.apache.hadoop.yarn.server.webapp.dao.ContainersInfo -->
  <class name="ContainersInfo" extends="java.lang.Object"
    abstract="false"
    static="false" final="false" visibility="public"
    deprecated="not deprecated">
    <constructor name="ContainersInfo"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </constructor>
    <method name="add"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
      <param name="containerInfo" type="org.apache.hadoop.yarn.server.webapp.dao.ContainerInfo"/>
    </method>
    <method name="getContainers" return="java.util.ArrayList"
      abstract="false" native="false" synchronized="false"
      static="false" final="false" visibility="public"
      deprecated="not deprecated">
    </method>
    <field name="container" type="java.util.ArrayList"
      transient="false" volatile="false"
      static="false" final="false" visibility="protected"
      deprecated="not deprecated">
    </field>
  </class>
  <!-- end class org.apache.hadoop.yarn.server.webapp.dao.ContainersInfo -->
</package>

</api>
